import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAuthenticationLogIncludeSchema } from '../inputTypeSchemas/UserAuthenticationLogIncludeSchema'
import { UserAuthenticationLogWhereInputSchema } from '../inputTypeSchemas/UserAuthenticationLogWhereInputSchema'
import { UserAuthenticationLogOrderByWithRelationInputSchema } from '../inputTypeSchemas/UserAuthenticationLogOrderByWithRelationInputSchema'
import { UserAuthenticationLogWhereUniqueInputSchema } from '../inputTypeSchemas/UserAuthenticationLogWhereUniqueInputSchema'
import { UserAuthenticationLogScalarFieldEnumSchema } from '../inputTypeSchemas/UserAuthenticationLogScalarFieldEnumSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserAuthenticationLogSelectSchema: z.ZodType<Prisma.UserAuthenticationLogSelect> = z.object({
  id: z.boolean().optional(),
  user_id: z.boolean().optional(),
  ipAddress: z.boolean().optional(),
  userAgent: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const UserAuthenticationLogFindFirstArgsSchema: z.ZodType<Prisma.UserAuthenticationLogFindFirstArgs> = z.object({
  select: UserAuthenticationLogSelectSchema.optional(),
  include: z.lazy(() => UserAuthenticationLogIncludeSchema).optional(),
  where: UserAuthenticationLogWhereInputSchema.optional(),
  orderBy: z.union([ UserAuthenticationLogOrderByWithRelationInputSchema.array(),UserAuthenticationLogOrderByWithRelationInputSchema ]).optional(),
  cursor: UserAuthenticationLogWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ UserAuthenticationLogScalarFieldEnumSchema,UserAuthenticationLogScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export default UserAuthenticationLogFindFirstArgsSchema;
