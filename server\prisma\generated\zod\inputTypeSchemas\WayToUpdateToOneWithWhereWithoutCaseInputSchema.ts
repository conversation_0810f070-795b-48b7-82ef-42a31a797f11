import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayToWhereInputSchema } from './WayToWhereInputSchema';
import { WayToUpdateWithoutCaseInputSchema } from './WayToUpdateWithoutCaseInputSchema';
import { WayToUncheckedUpdateWithoutCaseInputSchema } from './WayToUncheckedUpdateWithoutCaseInputSchema';

export const WayToUpdateToOneWithWhereWithoutCaseInputSchema: z.ZodType<Prisma.WayToUpdateToOneWithWhereWithoutCaseInput> = z.object({
  where: z.lazy(() => WayToWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => WayToUpdateWithoutCaseInputSchema),z.lazy(() => WayToUncheckedUpdateWithoutCaseInputSchema) ]),
}).strict();

export default WayToUpdateToOneWithWhereWithoutCaseInputSchema;
