import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserRatingIncludeSchema } from '../inputTypeSchemas/UserRatingIncludeSchema'
import { UserRatingWhereInputSchema } from '../inputTypeSchemas/UserRatingWhereInputSchema'
import { UserRatingOrderByWithRelationInputSchema } from '../inputTypeSchemas/UserRatingOrderByWithRelationInputSchema'
import { UserRatingWhereUniqueInputSchema } from '../inputTypeSchemas/UserRatingWhereUniqueInputSchema'
import { UserRatingScalarFieldEnumSchema } from '../inputTypeSchemas/UserRatingScalarFieldEnumSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserRatingSelectSchema: z.ZodType<Prisma.UserRatingSelect> = z.object({
  id: z.boolean().optional(),
  rating: z.boolean().optional(),
  comment: z.boolean().optional(),
  userId: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  senderId: z.boolean().optional(),
  caseId: z.boolean().optional(),
  confirm: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
  sender: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const UserRatingFindFirstOrThrowArgsSchema: z.ZodType<Prisma.UserRatingFindFirstOrThrowArgs> = z.object({
  select: UserRatingSelectSchema.optional(),
  include: z.lazy(() => UserRatingIncludeSchema).optional(),
  where: UserRatingWhereInputSchema.optional(),
  orderBy: z.union([ UserRatingOrderByWithRelationInputSchema.array(),UserRatingOrderByWithRelationInputSchema ]).optional(),
  cursor: UserRatingWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ UserRatingScalarFieldEnumSchema,UserRatingScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export default UserRatingFindFirstOrThrowArgsSchema;
