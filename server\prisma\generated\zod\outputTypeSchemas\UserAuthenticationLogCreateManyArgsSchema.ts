import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAuthenticationLogCreateManyInputSchema } from '../inputTypeSchemas/UserAuthenticationLogCreateManyInputSchema'

export const UserAuthenticationLogCreateManyArgsSchema: z.ZodType<Prisma.UserAuthenticationLogCreateManyArgs> = z.object({
  data: z.union([ UserAuthenticationLogCreateManyInputSchema,UserAuthenticationLogCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export default UserAuthenticationLogCreateManyArgsSchema;
