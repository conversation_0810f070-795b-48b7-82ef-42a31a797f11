import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayMiddleWhereUniqueInputSchema } from './WayMiddleWhereUniqueInputSchema';
import { WayMiddleCreateWithoutCaseInputSchema } from './WayMiddleCreateWithoutCaseInputSchema';
import { WayMiddleUncheckedCreateWithoutCaseInputSchema } from './WayMiddleUncheckedCreateWithoutCaseInputSchema';

export const WayMiddleCreateOrConnectWithoutCaseInputSchema: z.ZodType<Prisma.WayMiddleCreateOrConnectWithoutCaseInput> = z.object({
  where: z.lazy(() => WayMiddleWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => WayMiddleCreateWithoutCaseInputSchema),z.lazy(() => WayMiddleUncheckedCreateWithoutCaseInputSchema) ]),
}).strict();

export default WayMiddleCreateOrConnectWithoutCaseInputSchema;
