import { useState } from 'react'
import { Button, Input } from "@heroui/react"
import { LockIcon } from './LockIcon'
import { MailIcon } from '../lib/svg/MailIcon'
import { Card, CardHeader, CardBody, CardFooter } from "@heroui/react"
import { useUserStore } from '../store'
import { toast } from 'react-toastify'
import { useTranslation } from 'react-i18next'
import { Link, useRouter } from '@tanstack/react-router'
import { authClient } from '@/lib/auth'

export default function Login() {
  const userStore = useUserStore((state) => state)
  const [login, setLogin] = useState('<EMAIL>')
  const [password, setPassword] = useState('112233')
  const { t } = useTranslation()
  const router = useRouter()

  const [isLoading, setIsLoading] = useState(false)

  async function loginHandler() {
    try {
      console.log('Login handler called with:', { login, password })
      setIsLoading(true)

      // Используем better-auth клиент для входа
      const result = await authClient.signIn.email({
        email: login,
        password: password,
      })

      console.log('Login result:', result)

      if (result.data?.user) {
        // Сохраняем данные пользователя в store
        userStore.setData(result.data.user as any)
        toast.success(t('Login successful'))
        router.navigate({ to: '/' })
      } else if (result.error) {
        toast.error(result.error.message || t('Login failed'))
      }
    } catch (error) {
      console.error('Login error:', error)
      toast.error(t('Server error'))
    } finally {
      setIsLoading(false)
    }
  }
  return (
    <div>
      <Card shadow='lg'>
        <CardHeader className='flex gap-3 border-0'>
          <div className='font-semibold text-lg'>
            <h2>{t('Sign in')}</h2>
          </div>
        </CardHeader>
        <CardBody>
          <div className='flex flex-col space-y-3'>
            <Input
              onChange={(e) => setLogin(e.target.value)}
              autoFocus
              endContent={<MailIcon className='text-2xl text-default-400 pointer-events-none shrink-0' />}
              label='E-mail'
              placeholder='E-mail'
              value={login}
              variant='flat'
            />
            <Input
              onChange={(e) => setPassword(e.target.value)}
              endContent={<LockIcon className='text-2xl text-default-400 pointer-events-none shrink-0' />}
              label={t('Password')}
              value={password}
              placeholder={t('Password')}
              type='password'
              variant='flat'
            />
          </div>
        </CardBody>
        <CardFooter className='flex flex-wrap items-end justify-end'>
          <div>
            <Button className='font-semibold' variant='flat' isLoading={isLoading} color='primary' onPress={loginHandler}>
              {t('Sign in')}
            </Button>
          </div>
        </CardFooter>
      </Card>
      <div className='flex justify-center mt-5'>
        <Button variant='flat'>
          {/* <Link to='/reg'>{t('Sign up')}</Link> */}
          <Link to='/reg'>{t('New user')} ?</Link>
        </Button>
      </div>
    </div>
  )
}
