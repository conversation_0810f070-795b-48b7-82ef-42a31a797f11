import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarSelectSchema } from '../inputTypeSchemas/UserAvatarSelectSchema';
import { UserAvatarIncludeSchema } from '../inputTypeSchemas/UserAvatarIncludeSchema';

export const UserAvatarArgsSchema: z.ZodType<Prisma.UserAvatarDefaultArgs> = z.object({
  select: z.lazy(() => UserAvatarSelectSchema).optional(),
  include: z.lazy(() => UserAvatarIncludeSchema).optional(),
}).strict();

export default UserAvatarArgsSchema;
