import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayFromUpdateManyMutationInputSchema } from '../inputTypeSchemas/WayFromUpdateManyMutationInputSchema'
import { WayFromUncheckedUpdateManyInputSchema } from '../inputTypeSchemas/WayFromUncheckedUpdateManyInputSchema'
import { WayFromWhereInputSchema } from '../inputTypeSchemas/WayFromWhereInputSchema'

export const WayFromUpdateManyArgsSchema: z.ZodType<Prisma.WayFromUpdateManyArgs> = z.object({
  data: z.union([ WayFromUpdateManyMutationInputSchema,WayFromUncheckedUpdateManyInputSchema ]),
  where: WayFromWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default WayFromUpdateManyArgsSchema;
