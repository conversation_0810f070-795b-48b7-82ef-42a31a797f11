import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserUpdateWithoutAuthoredCasesInputSchema } from './UserUpdateWithoutAuthoredCasesInputSchema';
import { UserUncheckedUpdateWithoutAuthoredCasesInputSchema } from './UserUncheckedUpdateWithoutAuthoredCasesInputSchema';
import { UserCreateWithoutAuthoredCasesInputSchema } from './UserCreateWithoutAuthoredCasesInputSchema';
import { UserUncheckedCreateWithoutAuthoredCasesInputSchema } from './UserUncheckedCreateWithoutAuthoredCasesInputSchema';
import { UserWhereInputSchema } from './UserWhereInputSchema';

export const UserUpsertWithoutAuthoredCasesInputSchema: z.ZodType<Prisma.UserUpsertWithoutAuthoredCasesInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutAuthoredCasesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutAuthoredCasesInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutAuthoredCasesInputSchema),z.lazy(() => UserUncheckedCreateWithoutAuthoredCasesInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export default UserUpsertWithoutAuthoredCasesInputSchema;
