import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarWhereInputSchema } from '../inputTypeSchemas/UserAvatarWhereInputSchema'
import { UserAvatarOrderByWithRelationInputSchema } from '../inputTypeSchemas/UserAvatarOrderByWithRelationInputSchema'
import { UserAvatarWhereUniqueInputSchema } from '../inputTypeSchemas/UserAvatarWhereUniqueInputSchema'

export const UserAvatarAggregateArgsSchema: z.ZodType<Prisma.UserAvatarAggregateArgs> = z.object({
  where: UserAvatarWhereInputSchema.optional(),
  orderBy: z.union([ UserAvatarOrderByWithRelationInputSchema.array(),UserAvatarOrderByWithRelationInputSchema ]).optional(),
  cursor: UserAvatarWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default UserAvatarAggregateArgsSchema;
