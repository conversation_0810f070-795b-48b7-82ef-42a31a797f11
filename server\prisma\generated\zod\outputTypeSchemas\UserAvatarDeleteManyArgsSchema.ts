import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarWhereInputSchema } from '../inputTypeSchemas/UserAvatarWhereInputSchema'

export const UserAvatarDeleteManyArgsSchema: z.ZodType<Prisma.UserAvatarDeleteManyArgs> = z.object({
  where: UserAvatarWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default UserAvatarDeleteManyArgsSchema;
