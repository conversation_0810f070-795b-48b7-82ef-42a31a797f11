import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayFromUpdateWithoutCaseInputSchema } from './WayFromUpdateWithoutCaseInputSchema';
import { WayFromUncheckedUpdateWithoutCaseInputSchema } from './WayFromUncheckedUpdateWithoutCaseInputSchema';
import { WayFromCreateWithoutCaseInputSchema } from './WayFromCreateWithoutCaseInputSchema';
import { WayFromUncheckedCreateWithoutCaseInputSchema } from './WayFromUncheckedCreateWithoutCaseInputSchema';
import { WayFromWhereInputSchema } from './WayFromWhereInputSchema';

export const WayFromUpsertWithoutCaseInputSchema: z.ZodType<Prisma.WayFromUpsertWithoutCaseInput> = z.object({
  update: z.union([ z.lazy(() => WayFromUpdateWithoutCaseInputSchema),z.lazy(() => WayFromUncheckedUpdateWithoutCaseInputSchema) ]),
  create: z.union([ z.lazy(() => WayFromCreateWithoutCaseInputSchema),z.lazy(() => WayFromUncheckedCreateWithoutCaseInputSchema) ]),
  where: z.lazy(() => WayFromWhereInputSchema).optional()
}).strict();

export default WayFromUpsertWithoutCaseInputSchema;
