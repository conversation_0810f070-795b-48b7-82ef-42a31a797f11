import { z } from 'zod';
import type { <PERSON>risma } from '@prisma/client';
import { FavoriteIncludeSchema } from '../inputTypeSchemas/FavoriteIncludeSchema'
import { FavoriteWhereUniqueInputSchema } from '../inputTypeSchemas/FavoriteWhereUniqueInputSchema'
import { FavoriteCreateInputSchema } from '../inputTypeSchemas/FavoriteCreateInputSchema'
import { FavoriteUncheckedCreateInputSchema } from '../inputTypeSchemas/FavoriteUncheckedCreateInputSchema'
import { FavoriteUpdateInputSchema } from '../inputTypeSchemas/FavoriteUpdateInputSchema'
import { FavoriteUncheckedUpdateInputSchema } from '../inputTypeSchemas/FavoriteUncheckedUpdateInputSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const FavoriteSelectSchema: z.ZodType<Prisma.FavoriteSelect> = z.object({
  id: z.boolean().optional(),
  userId: z.boolean().optional(),
  caseId: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const FavoriteUpsertArgsSchema: z.ZodType<Prisma.FavoriteUpsertArgs> = z.object({
  select: FavoriteSelectSchema.optional(),
  include: z.lazy(() => FavoriteIncludeSchema).optional(),
  where: FavoriteWhereUniqueInputSchema,
  create: z.union([ FavoriteCreateInputSchema,FavoriteUncheckedCreateInputSchema ]),
  update: z.union([ FavoriteUpdateInputSchema,FavoriteUncheckedUpdateInputSchema ]),
}).strict() ;

export default FavoriteUpsertArgsSchema;
