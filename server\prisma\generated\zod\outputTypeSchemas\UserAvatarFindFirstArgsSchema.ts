import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarIncludeSchema } from '../inputTypeSchemas/UserAvatarIncludeSchema'
import { UserAvatarWhereInputSchema } from '../inputTypeSchemas/UserAvatarWhereInputSchema'
import { UserAvatarOrderByWithRelationInputSchema } from '../inputTypeSchemas/UserAvatarOrderByWithRelationInputSchema'
import { UserAvatarWhereUniqueInputSchema } from '../inputTypeSchemas/UserAvatarWhereUniqueInputSchema'
import { UserAvatarScalarFieldEnumSchema } from '../inputTypeSchemas/UserAvatarScalarFieldEnumSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserAvatarSelectSchema: z.ZodType<Prisma.UserAvatarSelect> = z.object({
  id: z.boolean().optional(),
  userId: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  base64string: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const UserAvatarFindFirstArgsSchema: z.ZodType<Prisma.UserAvatarFindFirstArgs> = z.object({
  select: UserAvatarSelectSchema.optional(),
  include: z.lazy(() => UserAvatarIncludeSchema).optional(),
  where: UserAvatarWhereInputSchema.optional(),
  orderBy: z.union([ UserAvatarOrderByWithRelationInputSchema.array(),UserAvatarOrderByWithRelationInputSchema ]).optional(),
  cursor: UserAvatarWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ UserAvatarScalarFieldEnumSchema,UserAvatarScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export default UserAvatarFindFirstArgsSchema;
