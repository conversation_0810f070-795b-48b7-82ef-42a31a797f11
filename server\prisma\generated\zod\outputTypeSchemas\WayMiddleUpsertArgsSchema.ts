import { z } from 'zod';
import type { <PERSON>risma } from '@prisma/client';
import { WayMiddleIncludeSchema } from '../inputTypeSchemas/WayMiddleIncludeSchema'
import { WayMiddleWhereUniqueInputSchema } from '../inputTypeSchemas/WayMiddleWhereUniqueInputSchema'
import { WayMiddleCreateInputSchema } from '../inputTypeSchemas/WayMiddleCreateInputSchema'
import { WayMiddleUncheckedCreateInputSchema } from '../inputTypeSchemas/WayMiddleUncheckedCreateInputSchema'
import { WayMiddleUpdateInputSchema } from '../inputTypeSchemas/WayMiddleUpdateInputSchema'
import { WayMiddleUncheckedUpdateInputSchema } from '../inputTypeSchemas/WayMiddleUncheckedUpdateInputSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const WayMiddleSelectSchema: z.ZodType<Prisma.WayMiddleSelect> = z.object({
  id: z.boolean().optional(),
  geometa: z.boolean().optional(),
  date: z.boolean().optional(),
  lat: z.boolean().optional(),
  lon: z.boolean().optional(),
  caseId: z.boolean().optional(),
  comment: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
}).strict()

export const WayMiddleUpsertArgsSchema: z.ZodType<Prisma.WayMiddleUpsertArgs> = z.object({
  select: WayMiddleSelectSchema.optional(),
  include: z.lazy(() => WayMiddleIncludeSchema).optional(),
  where: WayMiddleWhereUniqueInputSchema,
  create: z.union([ WayMiddleCreateInputSchema,WayMiddleUncheckedCreateInputSchema ]),
  update: z.union([ WayMiddleUpdateInputSchema,WayMiddleUncheckedUpdateInputSchema ]),
}).strict() ;

export default WayMiddleUpsertArgsSchema;
