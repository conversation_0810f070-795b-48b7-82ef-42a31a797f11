import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayMiddleWhereInputSchema } from '../inputTypeSchemas/WayMiddleWhereInputSchema'
import { WayMiddleOrderByWithRelationInputSchema } from '../inputTypeSchemas/WayMiddleOrderByWithRelationInputSchema'
import { WayMiddleWhereUniqueInputSchema } from '../inputTypeSchemas/WayMiddleWhereUniqueInputSchema'

export const WayMiddleAggregateArgsSchema: z.ZodType<Prisma.WayMiddleAggregateArgs> = z.object({
  where: WayMiddleWhereInputSchema.optional(),
  orderBy: z.union([ WayMiddleOrderByWithRelationInputSchema.array(),WayMiddleOrderByWithRelationInputSchema ]).optional(),
  cursor: WayMiddleWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default WayMiddleAggregateArgsSchema;
