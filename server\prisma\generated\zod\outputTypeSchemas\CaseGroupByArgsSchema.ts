import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { CaseWhereInputSchema } from '../inputTypeSchemas/CaseWhereInputSchema'
import { CaseOrderByWithAggregationInputSchema } from '../inputTypeSchemas/CaseOrderByWithAggregationInputSchema'
import { CaseScalarFieldEnumSchema } from '../inputTypeSchemas/CaseScalarFieldEnumSchema'
import { CaseScalarWhereWithAggregatesInputSchema } from '../inputTypeSchemas/CaseScalarWhereWithAggregatesInputSchema'

export const CaseGroupByArgsSchema: z.ZodType<Prisma.CaseGroupByArgs> = z.object({
  where: CaseWhereInputSchema.optional(),
  orderBy: z.union([ CaseOrderByWithAggregationInputSchema.array(),CaseOrderByWithAggregationInputSchema ]).optional(),
  by: CaseScalarFieldEnumSchema.array(),
  having: CaseScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default CaseGroupByArgsSchema;
