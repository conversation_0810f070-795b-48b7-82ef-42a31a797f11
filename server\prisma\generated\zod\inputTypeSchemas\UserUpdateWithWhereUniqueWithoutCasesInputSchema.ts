import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateWithoutCasesInputSchema } from './UserUpdateWithoutCasesInputSchema';
import { UserUncheckedUpdateWithoutCasesInputSchema } from './UserUncheckedUpdateWithoutCasesInputSchema';

export const UserUpdateWithWhereUniqueWithoutCasesInputSchema: z.ZodType<Prisma.UserUpdateWithWhereUniqueWithoutCasesInput> = z.object({
  where: z.lazy(() => UserWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => UserUpdateWithoutCasesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutCasesInputSchema) ]),
}).strict();

export default UserUpdateWithWhereUniqueWithoutCasesInputSchema;
