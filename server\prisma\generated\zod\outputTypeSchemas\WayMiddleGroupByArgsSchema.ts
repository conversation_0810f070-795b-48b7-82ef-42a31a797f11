import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayMiddleWhereInputSchema } from '../inputTypeSchemas/WayMiddleWhereInputSchema'
import { WayMiddleOrderByWithAggregationInputSchema } from '../inputTypeSchemas/WayMiddleOrderByWithAggregationInputSchema'
import { WayMiddleScalarFieldEnumSchema } from '../inputTypeSchemas/WayMiddleScalarFieldEnumSchema'
import { WayMiddleScalarWhereWithAggregatesInputSchema } from '../inputTypeSchemas/WayMiddleScalarWhereWithAggregatesInputSchema'

export const WayMiddleGroupByArgsSchema: z.ZodType<Prisma.WayMiddleGroupByArgs> = z.object({
  where: WayMiddleWhereInputSchema.optional(),
  orderBy: z.union([ WayMiddleOrderByWithAggregationInputSchema.array(),WayMiddleOrderByWithAggregationInputSchema ]).optional(),
  by: WayMiddleScalarFieldEnumSchema.array(),
  having: WayMiddleScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default WayMiddleGroupByArgsSchema;
