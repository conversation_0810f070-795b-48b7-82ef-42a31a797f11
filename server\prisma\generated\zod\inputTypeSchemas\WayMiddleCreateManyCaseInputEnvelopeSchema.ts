import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayMiddleCreateManyCaseInputSchema } from './WayMiddleCreateManyCaseInputSchema';

export const WayMiddleCreateManyCaseInputEnvelopeSchema: z.ZodType<Prisma.WayMiddleCreateManyCaseInputEnvelope> = z.object({
  data: z.union([ z.lazy(() => WayMiddleCreateManyCaseInputSchema),z.lazy(() => WayMiddleCreateManyCaseInputSchema).array() ]),
  skipDuplicates: z.boolean().optional()
}).strict();

export default WayMiddleCreateManyCaseInputEnvelopeSchema;
