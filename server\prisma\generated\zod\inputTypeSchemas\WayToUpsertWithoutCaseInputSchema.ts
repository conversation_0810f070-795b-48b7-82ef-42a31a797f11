import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayToUpdateWithoutCaseInputSchema } from './WayToUpdateWithoutCaseInputSchema';
import { WayToUncheckedUpdateWithoutCaseInputSchema } from './WayToUncheckedUpdateWithoutCaseInputSchema';
import { WayToCreateWithoutCaseInputSchema } from './WayToCreateWithoutCaseInputSchema';
import { WayToUncheckedCreateWithoutCaseInputSchema } from './WayToUncheckedCreateWithoutCaseInputSchema';
import { WayToWhereInputSchema } from './WayToWhereInputSchema';

export const WayToUpsertWithoutCaseInputSchema: z.ZodType<Prisma.WayToUpsertWithoutCaseInput> = z.object({
  update: z.union([ z.lazy(() => WayToUpdateWithoutCaseInputSchema),z.lazy(() => WayToUncheckedUpdateWithoutCaseInputSchema) ]),
  create: z.union([ z.lazy(() => WayToCreateWithoutCaseInputSchema),z.lazy(() => WayToUncheckedCreateWithoutCaseInputSchema) ]),
  where: z.lazy(() => WayToWhereInputSchema).optional()
}).strict();

export default WayToUpsertWithoutCaseInputSchema;
