import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAuthenticationLogWhereInputSchema } from '../inputTypeSchemas/UserAuthenticationLogWhereInputSchema'

export const UserAuthenticationLogDeleteManyArgsSchema: z.ZodType<Prisma.UserAuthenticationLogDeleteManyArgs> = z.object({
  where: UserAuthenticationLogWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default UserAuthenticationLogDeleteManyArgsSchema;
