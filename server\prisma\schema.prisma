// Генератор Prisma Client для работы с базой данных
generator client {
  provider = "prisma-client-js"
}

// Генератор Zod схем для валидации типов на основе Prisma моделей
// Создает TypeScript типы и Zod валидаторы для безопасной работы с данными
generator zod {
  provider                         = "zod-prisma-types"
  output                           = "./generated/zod"
  createOptionalDefaultValuesTypes = "true"  // Создает типы с опциональными значениями по умолчанию
  validateWhereUniqueInput         = "true"  // Валидация уникальных полей
  createRelationValuesTypes        = "true"  // Создает типы для связанных данных
  createPartialTypes               = "true"  // Создает частичные типы для обновлений
  writeBarrelFiles                 = "true"  // Создает index файлы для удобного импорта
  useMultipleFiles                 = "true"  // Разделяет типы по отдельным файлам
}

// Подключение к базе данных MySQL
datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

/// Основная модель пользователя системы takeNpass
/// Содержит всю информацию о пользователе, его настройки и связи с другими сущностями
model User {
  // Основные поля пользователя
  id                    String                  @id @unique @default(uuid()) // Уникальный идентификатор пользователя
  username              String?                                              // Имя пользователя (опционально)
  email                 String                  @unique                      // Email адрес (обязательный, уникальный)
  emailVerified         Boolean                 @default(false)              // Статус верификации email
  image                 String?                                              // URL изображения профиля
  name                  String?                                              // Полное имя пользователя
  
  // Временные метки
  createdAt             DateTime                @default(now())              // Дата создания аккаунта
  updatedAt             DateTime                @updatedAt                   // Дата последнего обновления
  
  // Дополнительная информация профиля
  about_me              String?                                              // Описание пользователя
  phone                 String?                 @unique                      // Номер телефона (уникальный)
  
  // Статусы и настройки
  blocked               Boolean                 @default(false)              // Заблокирован ли пользователь
  confirmed             Boolean                 @default(false)              // Подтвержден ли аккаунт
  role                  Roles                   @default(REG)                // Роль пользователя в системе
  language              String                  @default("en")               // Предпочитаемый язык интерфейса
  
  // Связи с другими моделями
  authoredCases         Case[]                  @relation("caseAuthor")       // Кейсы, созданные пользователем
  favorites             Favorite[]              @relation("UserFavs")         // Избранные кейсы пользователя
  accounts              Account[]                                            // Связанные аккаунты (OAuth и др.)
  receivedMessages      Message[]               @relation("ReceivedMessages") // Полученные сообщения
  sentMessages          Message[]               @relation("SentMessages")     // Отправленные сообщения
  sendedNotifications   Notification[]          @relation("SendedNotifications") // Отправленные уведомления
  notifications         Notification[]          @relation("UserNotifications")   // Полученные уведомления
  notificationSettings  NotificationSettings[]  @relation("UserNotificationSettings") // Настройки уведомлений
  auth_session          Session[]                                            // Активные сессии пользователя
  userAuthenticationLog UserAuthenticationLog[]                             // Лог аутентификации
  avatar                UserAvatar?             @relation("UserAvatar")      // Аватар пользователя
  sendedRatings         UserRating[]            @relation("SendedRatings")   // Отправленные рейтинги
  ratings               UserRating[]            @relation("UserRatings")     // Полученные рейтинги
  cases                 Case[]                  @relation("caseParticipant") // Кейсы, в которых участвует пользователь
}

/// Настройки уведомлений для пользователей
/// Определяет способы доставки уведомлений и их активность
model NotificationSettings {
  id     String             @id @unique @default(uuid()) // Уникальный идентификатор настройки
  userId String                                          // ID пользователя, которому принадлежат настройки
  method NotificationMethod                              // Способ доставки уведомления (EMAIL, SMS, TELEGRAM, WHATSAPP)
  active Boolean            @default(true)              // Активна ли данная настройка уведомлений
  user   User               @relation("UserNotificationSettings", fields: [userId], references: [id]) // Связь с пользователем

  @@index([userId], map: "NotificationSettings_userId_fkey")
}

/// Система рейтингов пользователей
/// Позволяет пользователям оценивать друг друга после завершения кейсов
model UserRating {
  id        Int      @id @default(autoincrement())       // Уникальный идентификатор рейтинга
  rating    Int                                           // Оценка (обычно от 1 до 5)
  comment   String?                                       // Комментарий к оценке (опционально)
  userId    String                                        // ID пользователя, которого оценивают
  createdAt DateTime @default(now())                     // Дата создания рейтинга
  updatedAt DateTime @updatedAt                          // Дата последнего обновления
  senderId  String                                        // ID пользователя, который ставит оценку
  caseId    String                                        // ID кейса, в рамках которого ставится оценка
  confirm   Boolean? @default(false)                     // Подтвержден ли рейтинг
  
  // Связи с другими моделями
  case      Case     @relation(fields: [caseId], references: [id])                    // Связь с кейсом
  sender    User     @relation("SendedRatings", fields: [senderId], references: [id]) // Пользователь, который ставит оценку
  user      User     @relation("UserRatings", fields: [userId], references: [id])     // Пользователь, которого оценивают

  @@unique([caseId, userId, senderId]) // Один пользователь может оценить другого только один раз в рамках одного кейса
  @@index([senderId], map: "UserRating_senderId_fkey")
  @@index([userId], map: "UserRating_userId_fkey")
}

/// Аватары пользователей
/// Хранит изображения профиля пользователей в формате base64
model UserAvatar {
  id           String   @id @unique @default(uuid()) // Уникальный идентификатор аватара
  userId       String   @unique                       // ID пользователя (один аватар на пользователя)
  createdAt    DateTime @default(now())               // Дата загрузки аватара
  updatedAt    DateTime @updatedAt                    // Дата последнего обновления
  base64string String   @db.LongText                  // Изображение в формате base64
  user         User     @relation("UserAvatar", fields: [userId], references: [id]) // Связь с пользователем
}

model Session {
  id        String   @id
  userId    String
  expiresAt DateTime
  token     String   @unique
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(references: [id], fields: [userId], onDelete: Cascade)

  @@index([userId])
  @@index([token])
}

model Account {
  id                String  @id @default(uuid())
  accountId         String?
  userId            String
  providerId        String
  providerAccountId String?
  password          String?
  accessToken       String?
  refreshToken      String?
  expiresAt         DateTime?
  tokenType         String?
  scope             String?
  idToken           String?
  sessionState      String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  id         String   @id @default(uuid())
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model UserAuthenticationLog {
  id        Int      @id @default(autoincrement())
  user_id   String
  ipAddress String
  userAgent String
  createdAt DateTime @default(now())
  user      User     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([user_id], map: "UserAuthenticationLog_user_id_fkey")
}

model Message {
  id         Int      @id @default(autoincrement())
  createdAt  DateTime @default(now())
  content    String   @db.VarChar(254)
  senderId   String
  receiverId String
  read       Boolean? @default(false)
  receiver   User     @relation("ReceivedMessages", fields: [receiverId], references: [id])
  sender     User     @relation("SentMessages", fields: [senderId], references: [id])

  @@index([senderId, receiverId])
  @@index([receiverId], map: "Message_receiverId_fkey")
}

/// Система уведомлений
/// Хранит уведомления для пользователей о различных событиях в системе
model Notification {
  id             Int      @id @default(autoincrement())        // Уникальный идентификатор уведомления
  createdAt      DateTime @default(now())                     // Дата создания уведомления
  userId         String                                        // ID пользователя, которому предназначено уведомление
  read           Boolean? @default(false)                     // Прочитано ли уведомление
  caseId         String?                                       // ID связанного кейса (опционально)
  link           String?                                       // Ссылка для перехода из уведомления
  senderId       String?                                       // ID пользователя, отправившего уведомление
  translationKey String                                        // Ключ для перевода текста уведомления
  
  // Связи с другими моделями
  case           Case?    @relation("CaseNotifications", fields: [caseId], references: [id])     // Связь с кейсом
  sender         User?    @relation("SendedNotifications", fields: [senderId], references: [id]) // Отправитель уведомления
  user           User     @relation("UserNotifications", fields: [userId], references: [id])     // Получатель уведомления

  @@index([caseId], map: "Notification_caseId_fkey")
  @@index([senderId], map: "Notification_senderId_fkey")
  @@index([userId], map: "Notification_userId_fkey")
}

model Translation {
  id       Int     @id @default(autoincrement())
  key      String
  value    String  @db.MediumText
  language String
  includes String? @db.LongText

  @@unique([key, language], name: "key_lang")
}

/// Основная модель кейсов (заданий/поездок)
/// Представляет поездки или задачи, которые пользователи могут создавать и в которых могут участвовать
model Case {
  id            String         @id @unique @default(uuid()) // Уникальный идентификатор кейса
  createdAt     DateTime       @default(now())                // Дата создания кейса
  description   String?                                        // Описание кейса/поездки
  status        CaseStatus     @default(OPEN)                 // Статус кейса (OPEN, CLOSED, ARCHIVED, DONE, CANCELED, TEST)
  authorId      String                                         // ID автора кейса
  baggage       String?        @db.LongText                   // Информация о багаже (JSON)
  price         Float?         @default(0)                    // Цена за участие/услугу
  isRequest     Boolean        @default(false)                // Является ли кейс запросом (true) или предложением (false)
  expire_at     DateTime?                                      // Дата истечения актуальности кейса
  
  // Связи с другими моделями
  author        User           @relation("caseAuthor", fields: [authorId], references: [id])       // Автор кейса
  favorites     Favorite[]     @relation("favs")                                                   // Пользователи, добавившие в избранное
  notifications Notification[] @relation("CaseNotifications")                                     // Уведомления, связанные с кейсом
  UserRating    UserRating[]                                                                       // Рейтинги участников кейса
  from          WayFrom?                                                                           // Точка отправления
  middlepoints  WayMiddle[]                                                                        // Промежуточные точки маршрута
  to            WayTo?                                                                             // Точка назначения
  clients       User[]         @relation("caseParticipant")                                       // Участники кейса

  @@index([authorId])
}

/// Избранные кейсы пользователей
/// Позволяет пользователям сохранять интересные кейсы в избранное
model Favorite {
  id     Int    @id @default(autoincrement()) // Уникальный идентификатор записи в избранном
  userId String                                // ID пользователя
  caseId String                                // ID кейса, добавленного в избранное
  
  // Связи с другими моделями
  case   Case   @relation("favs", fields: [caseId], references: [id], onDelete: Cascade) // Связь с кейсом
  user   User   @relation("UserFavs", fields: [userId], references: [id])              // Связь с пользователем

  @@index([userId, caseId]) // Индекс для быстрого поиска избранных кейсов пользователя
  @@index([caseId], map: "Favorite_caseId_fkey")
}

/// Точка отправления для кейса
/// Определяет начальную точку маршрута с координатами и временем
model WayFrom {
  id      Int       @id @default(autoincrement())  // Уникальный идентификатор точки отправления
  geometa Json                                      // Геометаданные точки (адрес, название и т.д.)
  date    DateTime?                                 // Дата и время отправления (опционально)
  lat     Decimal                                   // Широта
  lon     Decimal                                   // Долгота
  caseId  String    @unique                         // ID кейса (один кейс - одна точка отправления)
  comment String?                                   // Дополнительный комментарий к точке
  case    Case      @relation(fields: [caseId], references: [id], onDelete: Cascade) // Связь с кейсом

  @@index([caseId])
}

/// Точка назначения для кейса
/// Определяет конечную точку маршрута с координатами и временем прибытия
model WayTo {
  id      Int       @id @default(autoincrement())  // Уникальный идентификатор точки назначения
  geometa Json                                      // Геометаданные точки (адрес, название и т.д.)
  date    DateTime?                                 // Дата и время прибытия (опционально)
  lat     Decimal                                   // Широта
  lon     Decimal                                   // Долгота
  caseId  String    @unique                         // ID кейса (один кейс - одна точка назначения)
  comment String?                                   // Дополнительный комментарий к точке
  case    Case      @relation(fields: [caseId], references: [id], onDelete: Cascade) // Связь с кейсом

  @@index([caseId])
}

/// Промежуточные точки маршрута
/// Позволяет определить остановки между точками отправления и назначения
model WayMiddle {
  id      Int       @id @default(autoincrement())  // Уникальный идентификатор промежуточной точки
  geometa Json                                      // Геометаданные точки (адрес, название и т.д.)
  date    DateTime?                                 // Дата и время прохождения точки (опционально)
  lat     Decimal                                   // Широта
  lon     Decimal                                   // Долгота
  caseId  String                                    // ID кейса, к которому относится точка
  comment String?                                   // Дополнительный комментарий к точке
  case    Case      @relation(fields: [caseId], references: [id], onDelete: Cascade) // Связь с кейсом

  @@index([caseId])
}

/// Статусы кейсов в системе
enum CaseStatus {
  OPEN     // Открытый кейс, доступен для подачи заявок
  CLOSED   // Закрытый кейс, заявки не принимаются
  ARCHIVED // Архивный кейс
  DONE     // Выполненный кейс
  CANCELED // Отмененный кейс
  TEST     // Тестовый кейс
}

/// Роли пользователей в системе
enum Roles {
  GUEST // Гость (неавторизованный пользователь)
  REG   // Зарегистрированный пользователь
  MODER // Модератор
  ADMIN // Администратор
  SU    // Суперпользователь
}

/// Способы доставки уведомлений
enum NotificationMethod {
  EMAIL    // Уведомления по электронной почте
  SMS      // SMS-уведомления
  TELEGRAM // Уведомления через Telegram
  WHATSAPP // Уведомления через WhatsApp
}
