import { z } from 'zod';
import type { Prisma } from '@prisma/client';

export const CaseCountOutputTypeSelectSchema: z.ZodType<Prisma.CaseCountOutputTypeSelect> = z.object({
  favorites: z.boolean().optional(),
  notifications: z.boolean().optional(),
  UserRating: z.boolean().optional(),
  middlepoints: z.boolean().optional(),
  clients: z.boolean().optional(),
}).strict();

export default CaseCountOutputTypeSelectSchema;
