import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserRatingSelectSchema } from '../inputTypeSchemas/UserRatingSelectSchema';
import { UserRatingIncludeSchema } from '../inputTypeSchemas/UserRatingIncludeSchema';

export const UserRatingArgsSchema: z.ZodType<Prisma.UserRatingDefaultArgs> = z.object({
  select: z.lazy(() => UserRatingSelectSchema).optional(),
  include: z.lazy(() => UserRatingIncludeSchema).optional(),
}).strict();

export default UserRatingArgsSchema;
