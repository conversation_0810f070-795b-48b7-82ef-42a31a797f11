import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayToIncludeSchema } from '../inputTypeSchemas/WayToIncludeSchema'
import { WayToCreateInputSchema } from '../inputTypeSchemas/WayToCreateInputSchema'
import { WayToUncheckedCreateInputSchema } from '../inputTypeSchemas/WayToUncheckedCreateInputSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const WayToSelectSchema: z.ZodType<Prisma.WayToSelect> = z.object({
  id: z.boolean().optional(),
  geometa: z.boolean().optional(),
  date: z.boolean().optional(),
  lat: z.boolean().optional(),
  lon: z.boolean().optional(),
  caseId: z.boolean().optional(),
  comment: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
}).strict()

export const WayToCreateArgsSchema: z.ZodType<Prisma.WayToCreateArgs> = z.object({
  select: WayToSelectSchema.optional(),
  include: z.lazy(() => WayToIncludeSchema).optional(),
  data: z.union([ WayToCreateInputSchema,WayToUncheckedCreateInputSchema ]),
}).strict() ;

export default WayToCreateArgsSchema;
