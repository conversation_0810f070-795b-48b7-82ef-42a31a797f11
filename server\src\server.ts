import 'dotenv/config'

// import ws from '@fastify/websocket'
import fastify from 'fastify'
import fastifyIO from 'fastify-socket.io'
import fastifyCors from '@fastify/cors'
import type { FastifyCookieOptions } from '@fastify/cookie'
import cookie from '@fastify/cookie'
import { fastifyTRPCPlugin } from '@trpc/server/adapters/fastify'

import { appRouter } from './router'
import { createContext } from './router/context'
import { auth } from './providers/auth'

// trpc-playground удален из проекта
// import { applyWSSHandler } from '@trpc/server/adapters/ws'

import * as i18nextMiddleware from 'i18next-http-middleware'
import i18next from 'i18next'
import { $prisma } from './providers/prisma'
// import { WebSocketService } from './services/depr_ws'
import { Server } from 'socket.io'
import { IO } from './providers/ws'
import { CaseService } from './services/case'
import { serverConfig } from './config'

function runServicesAtMidnight(cbs: Array<() => void>) {
  console.log('🚀 ~ file: server.ts:24 ~ runServicesAtMidnight ~ process.env.NODE_APP_INSTANCE:', process.env.NODE_APP_INSTANCE)
  if (process.env.NODE_APP_INSTANCE === '0' || !process.env.NODE_APP_INSTANCE) {
    cbs.map((cb) => cb?.())

    const now = new Date()
    const millisTill00 = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 1, 0, 0, 0).getTime() - now.getTime()
    let millisTill = millisTill00

    if (millisTill00 < 0) {
      millisTill += 86400000
    } else {
      millisTill += 2 * 86400000
    }

    setTimeout(function () {
      cbs.map((cb) => cb?.())
      setInterval(function () {
        cbs.map((cb) => cb?.())
      }, 86400000)
    }, millisTill)
  }
}

// TODO: move to LangService
const loadLocalesFromDatabase = async () => {
  const translations = await $prisma.translation.findMany()
  const resources: any = {}

  translations.forEach((translation) => {
    const { key, value, language } = translation
    resources[language] ??= {}
    resources[language]['translation'] ??= {}
    resources[language]['translation'][key] = value
  })

  return resources
}

export interface ServerOptions {
  dev?: boolean
  port?: number
  prefix?: string
  app_secret?: string
}

export function createServer(opts: ServerOptions) {
  const dev = opts.dev ?? true
  const port = opts.port ?? 3000
  const prefix = opts.prefix ?? '/trpc'
  const server = fastify({ logger: dev })

  const trpcApiEndpoint = '/trpc'
  const playgroundEndpoint = '/playground'

  // server.register(require('@fastify/express'))

  async function regPlugins() {
    // Configure CORS policies
    await server.register(fastifyCors, {
      origin: process.env.CLIENT_ORIGIN || ["http://localhost:3000", "http://localhost:5173"],
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      allowedHeaders: [
        "Content-Type",
        "Authorization",
        "X-Requested-With"
      ],
      credentials: true,
      maxAge: 86400
    })

    // server.register(ws)
    await server.register(fastifyIO)

    await i18next
      // .use(i18nextMiddleware.LanguageDetector)
      .init({
        resources: await loadLocalesFromDatabase(),
        // debug: true,
        lng: 'en'
      })

    await server.register(i18nextMiddleware.plugin, {
      i18next
    })

    // server.addHook('preHandler', i18nextMiddleware.handle(i18next, {}))

    await server.register(fastifyTRPCPlugin, {
      prefix,
      // useWSS: true,
      trpcOptions: { router: appRouter, createContext }
    })

    // WebSocketService.init()

    server.register(cookie, {
      secret: serverConfig.app_secret, // for cookies signature
      parseOptions: {}
    } as FastifyCookieOptions)

    server.ready((err) => {
      if (err) throw err

      // server.websocketServer = server.io
      // WebSocketService.init()

      IO.init(server)

      // server.io.on('connection', (socket: any) => console.info('Socket connected!', socket.id))
    })

    server.get('/', async ({ i18n }) => {
      console.log('server.get ~ i18n:', i18n.language)
      return { hello: 'wait-on 💨' }
    })

    // Better Auth routes - рабочий обработчик
    const authHandler = async (request: any, reply: any) => {
      console.log(`🔐 Auth request: ${request.method} ${request.url}`);
      try {
        // Создаем Request объект
        const req = new Request(`http://localhost:3003${request.url}`, {
          method: request.method,
          headers: request.headers as any,
          body: request.body ? JSON.stringify(request.body) : undefined,
        });

        console.log('📤 Calling auth.handler...');
        const response = await auth.handler(req);
        console.log('📥 Response status:', response.status);

        // Правильная обработка ответа
        reply.status(response.status);
        
        // Копируем заголовки
        response.headers.forEach((value, key) => {
          reply.header(key, value);
        });
        
        if (response.body) {
          const text = await response.text();
          console.log('📦 Response body:', text);
          reply.send(text);
        } else {
          console.log('📦 Empty response body');
          reply.send(null);
        }

      } catch (error) {
        console.error("🚨 Authentication Error:", error);
        reply.status(500).send({ 
          error: "Internal authentication error",
          code: "AUTH_FAILURE",
          message: error.message
        });
      }
    };

    // Регистрируем основные маршруты better-auth
    console.log('🔐 Registering Better Auth routes...');

    // Используем рабочий authHandler для всех маршрутов
    server.get('/api/auth/session', authHandler);
    server.post('/api/auth/session', authHandler);
    server.get('/api/auth/get-session', authHandler);  // Добавляем маршрут для получения сессии
    server.get('/api/auth/sign-in', authHandler);
    server.post('/api/auth/sign-in', authHandler);
    server.post('/api/auth/sign-in/email', authHandler);  // Добавляем специфичный маршрут для email входа
    server.get('/api/auth/sign-up', authHandler);
    server.post('/api/auth/sign-up', authHandler);
    server.post('/api/auth/sign-up/email', authHandler);  // Добавляем специфичный маршрут для email регистрации
    server.post('/api/auth/sign-out', authHandler);
    server.get('/api/auth/error', authHandler);
    server.get('/api/auth/verify-email', authHandler);
    server.post('/api/auth/verify-email', authHandler);
    console.log('✅ Better Auth routes registered');

    // Тестовый маршрут для проверки
    server.get('/api/test', async (request, reply) => {
      return { message: 'Test route works!' };
    });

    // Простой тестовый auth маршрут
    server.get('/api/auth/test', async (request, reply) => {
      console.log('🧪 Test auth route called');
      return { message: 'Auth test route works!', url: request.url };
    });

    // Другой тестовый маршрут для проверки перезагрузки
    server.get('/api/auth/check', async (request, reply) => {
      console.log('✅ Check route called - server reloaded!');
      return { message: 'Server reloaded successfully!', timestamp: new Date().toISOString() };
    });

    // trpc-playground код удален
  }

  const stop = async () => {
    await server.close()
  }
  const start = async () => {
    try {
      await regPlugins()
      await server.listen({ port })
      console.log(`listening on ${port}`)

      runServicesAtMidnight([CaseService.onServerInit.bind(CaseService)])

      await i18next.changeLanguage('ru')
      console.log('i18next lang: ', i18next.languages)
      console.log('i18next test:', i18next.t('Hello'))
    } catch (err) {
      console.error('server ~ start ~ err:', err)
      server.log.error(err)
      process.exit(1)
    }
  }

  return { server, start, stop }
}

declare module 'fastify' {
  interface FastifyInstance {
    io: Server<{ hello: string }>
  }
}
