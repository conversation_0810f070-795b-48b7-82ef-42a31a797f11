import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserRatingUpdateManyMutationInputSchema } from '../inputTypeSchemas/UserRatingUpdateManyMutationInputSchema'
import { UserRatingUncheckedUpdateManyInputSchema } from '../inputTypeSchemas/UserRatingUncheckedUpdateManyInputSchema'
import { UserRatingWhereInputSchema } from '../inputTypeSchemas/UserRatingWhereInputSchema'

export const UserRatingUpdateManyArgsSchema: z.ZodType<Prisma.UserRatingUpdateManyArgs> = z.object({
  data: z.union([ UserRatingUpdateManyMutationInputSchema,UserRatingUncheckedUpdateManyInputSchema ]),
  where: UserRatingWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default UserRatingUpdateManyArgsSchema;
