import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserWhereInputSchema } from './UserWhereInputSchema';
import { UserUpdateWithoutUserAuthenticationLogInputSchema } from './UserUpdateWithoutUserAuthenticationLogInputSchema';
import { UserUncheckedUpdateWithoutUserAuthenticationLogInputSchema } from './UserUncheckedUpdateWithoutUserAuthenticationLogInputSchema';

export const UserUpdateToOneWithWhereWithoutUserAuthenticationLogInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutUserAuthenticationLogInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutUserAuthenticationLogInputSchema),z.lazy(() => UserUncheckedUpdateWithoutUserAuthenticationLogInputSchema) ]),
}).strict();

export default UserUpdateToOneWithWhereWithoutUserAuthenticationLogInputSchema;
