import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserWhereUniqueInputSchema } from './UserWhereUniqueInputSchema';
import { UserUpdateWithoutCasesInputSchema } from './UserUpdateWithoutCasesInputSchema';
import { UserUncheckedUpdateWithoutCasesInputSchema } from './UserUncheckedUpdateWithoutCasesInputSchema';
import { UserCreateWithoutCasesInputSchema } from './UserCreateWithoutCasesInputSchema';
import { UserUncheckedCreateWithoutCasesInputSchema } from './UserUncheckedCreateWithoutCasesInputSchema';

export const UserUpsertWithWhereUniqueWithoutCasesInputSchema: z.ZodType<Prisma.UserUpsertWithWhereUniqueWithoutCasesInput> = z.object({
  where: z.lazy(() => UserWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => UserUpdateWithoutCasesInputSchema),z.lazy(() => UserUncheckedUpdateWithoutCasesInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutCasesInputSchema),z.lazy(() => UserUncheckedCreateWithoutCasesInputSchema) ]),
}).strict();

export default UserUpsertWithWhereUniqueWithoutCasesInputSchema;
