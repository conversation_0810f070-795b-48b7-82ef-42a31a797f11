import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayMiddleIncludeSchema } from '../inputTypeSchemas/WayMiddleIncludeSchema'
import { WayMiddleWhereUniqueInputSchema } from '../inputTypeSchemas/WayMiddleWhereUniqueInputSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const WayMiddleSelectSchema: z.ZodType<Prisma.WayMiddleSelect> = z.object({
  id: z.boolean().optional(),
  geometa: z.boolean().optional(),
  date: z.boolean().optional(),
  lat: z.boolean().optional(),
  lon: z.boolean().optional(),
  caseId: z.boolean().optional(),
  comment: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
}).strict()

export const WayMiddleFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.WayMiddleFindUniqueOrThrowArgs> = z.object({
  select: WayMiddleSelectSchema.optional(),
  include: z.lazy(() => WayMiddleIncludeSchema).optional(),
  where: WayMiddleWhereUniqueInputSchema,
}).strict() ;

export default WayMiddleFindUniqueOrThrowArgsSchema;
