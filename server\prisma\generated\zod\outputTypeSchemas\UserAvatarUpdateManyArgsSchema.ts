import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarUpdateManyMutationInputSchema } from '../inputTypeSchemas/UserAvatarUpdateManyMutationInputSchema'
import { UserAvatarUncheckedUpdateManyInputSchema } from '../inputTypeSchemas/UserAvatarUncheckedUpdateManyInputSchema'
import { UserAvatarWhereInputSchema } from '../inputTypeSchemas/UserAvatarWhereInputSchema'

export const UserAvatarUpdateManyArgsSchema: z.ZodType<Prisma.UserAvatarUpdateManyArgs> = z.object({
  data: z.union([ UserAvatarUpdateManyMutationInputSchema,UserAvatarUncheckedUpdateManyInputSchema ]),
  where: UserAvatarWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default UserAvatarUpdateManyArgsSchema;
