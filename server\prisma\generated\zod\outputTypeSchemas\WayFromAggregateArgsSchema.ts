import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayFromWhereInputSchema } from '../inputTypeSchemas/WayFromWhereInputSchema'
import { WayFromOrderByWithRelationInputSchema } from '../inputTypeSchemas/WayFromOrderByWithRelationInputSchema'
import { WayFromWhereUniqueInputSchema } from '../inputTypeSchemas/WayFromWhereUniqueInputSchema'

export const WayFromAggregateArgsSchema: z.ZodType<Prisma.WayFromAggregateArgs> = z.object({
  where: WayFromWhereInputSchema.optional(),
  orderBy: z.union([ WayFromOrderByWithRelationInputSchema.array(),WayFromOrderByWithRelationInputSchema ]).optional(),
  cursor: WayFromWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default WayFromAggregateArgsSchema;
