import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayMiddleScalarWhereInputSchema } from './WayMiddleScalarWhereInputSchema';
import { WayMiddleUpdateManyMutationInputSchema } from './WayMiddleUpdateManyMutationInputSchema';
import { WayMiddleUncheckedUpdateManyWithoutCaseInputSchema } from './WayMiddleUncheckedUpdateManyWithoutCaseInputSchema';

export const WayMiddleUpdateManyWithWhereWithoutCaseInputSchema: z.ZodType<Prisma.WayMiddleUpdateManyWithWhereWithoutCaseInput> = z.object({
  where: z.lazy(() => WayMiddleScalarWhereInputSchema),
  data: z.union([ z.lazy(() => WayMiddleUpdateManyMutationInputSchema),z.lazy(() => WayMiddleUncheckedUpdateManyWithoutCaseInputSchema) ]),
}).strict();

export default WayMiddleUpdateManyWithWhereWithoutCaseInputSchema;
