import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarCreateManyInputSchema } from '../inputTypeSchemas/UserAvatarCreateManyInputSchema'

export const UserAvatarCreateManyArgsSchema: z.ZodType<Prisma.UserAvatarCreateManyArgs> = z.object({
  data: z.union([ UserAvatarCreateManyInputSchema,UserAvatarCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export default UserAvatarCreateManyArgsSchema;
