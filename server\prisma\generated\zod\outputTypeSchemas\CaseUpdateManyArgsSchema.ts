import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { CaseUpdateManyMutationInputSchema } from '../inputTypeSchemas/CaseUpdateManyMutationInputSchema'
import { CaseUncheckedUpdateManyInputSchema } from '../inputTypeSchemas/CaseUncheckedUpdateManyInputSchema'
import { CaseWhereInputSchema } from '../inputTypeSchemas/CaseWhereInputSchema'

export const CaseUpdateManyArgsSchema: z.ZodType<Prisma.CaseUpdateManyArgs> = z.object({
  data: z.union([ CaseUpdateManyMutationInputSchema,CaseUncheckedUpdateManyInputSchema ]),
  where: CaseWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default CaseUpdateManyArgsSchema;
