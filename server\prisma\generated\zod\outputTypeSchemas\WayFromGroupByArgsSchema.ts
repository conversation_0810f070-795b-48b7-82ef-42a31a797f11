import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayFromWhereInputSchema } from '../inputTypeSchemas/WayFromWhereInputSchema'
import { WayFromOrderByWithAggregationInputSchema } from '../inputTypeSchemas/WayFromOrderByWithAggregationInputSchema'
import { WayFromScalarFieldEnumSchema } from '../inputTypeSchemas/WayFromScalarFieldEnumSchema'
import { WayFromScalarWhereWithAggregatesInputSchema } from '../inputTypeSchemas/WayFromScalarWhereWithAggregatesInputSchema'

export const WayFromGroupByArgsSchema: z.ZodType<Prisma.WayFromGroupByArgs> = z.object({
  where: WayFromWhereInputSchema.optional(),
  orderBy: z.union([ WayFromOrderByWithAggregationInputSchema.array(),WayFromOrderByWithAggregationInputSchema ]).optional(),
  by: WayFromScalarFieldEnumSchema.array(),
  having: WayFromScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default WayFromGroupByArgsSchema;
