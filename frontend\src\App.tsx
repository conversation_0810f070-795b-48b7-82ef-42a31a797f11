import { useEffect, useState } from 'react'
import './App.css'
import 'react-toastify/dist/ReactToastify.css'

import { Button, CircularProgress, HeroUIProvider } from "@heroui/react"

import Login from './components/Login'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useUserStore } from './store'
// import { ToastBar, Toaster, toast } from 'react-toastify'
import { ToastContainer, toast } from 'react-toastify'

import { trpc } from './trpc'
import SuperJSON from 'superjson'
import { createWSClient, httpBatchLink, splitLink, wsLink } from '@trpc/client'

import ValidateUser from './components/ValidateUser'
import { Link, Outlet } from '@tanstack/react-router'
import { NavBar } from './components/Navbar'
import { useNetworkState } from '@uidotdev/usehooks'
import IconSignalWifiErrorLine from './lib/svg/WifiErrorIcon'
import { useTranslation } from 'react-i18next'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

import { socket } from './socket'

function App() {
  const network = useNetworkState()
  const userStore = useUserStore((state) => state)
  const { t } = useTranslation()

  const [wsIsConnect, setWsIsConnect] = useState(socket.connected)

  // const wsClient = createWSClient({
  //   url: `ws://localhost:5173/trpc`,
  //   onClose: () => setWsIsConnect(() => false),
  //   onOpen: () => setWsIsConnect(() => true),
  //   retryDelayMs: (attempIndex: number) => attempIndex + 150000
  //   // WebSocket
  // })

  const [queryClient] = useState(() => new QueryClient())
  const [trpcClient] = useState(() =>
    trpc.createClient({
      links: [
        // wsLink({
        //   client: wsClient
        // }),
        httpBatchLink({
          url: (import.meta.env['VITE_TRPC_URL'] as string) || '/trpc',
          transformer: SuperJSON
        })
      ]
      // links: [
      //   splitLink({
      //     condition(op) {
      //       return op.type === 'subscription'
      //     },
      //     true: wsLink({
      //       client: wsClient
      //     }),
      //     false: httpBatchLink({
      //       url: import.meta.env['VITE_TRPC_URL']
      //     })
      //   })
      // ]
    })
  )

  useEffect(() => {
    function onConnect() {
      setWsIsConnect(true)
    }

    function onDisconnect() {
      setWsIsConnect(false)
    }

    socket.on('connect', onConnect)
    socket.on('disconnect', onDisconnect)

    return () => {
      socket.off('connect', onConnect)
      socket.off('disconnect', onDisconnect)
    }
  }, [])

  const [isValidateUserLoading, setIsValidateUserLoading] = useState(false)

  const NotificationCloseButton = ({ closeToast }) => (
    <Button onClick={closeToast} size='sm' isIconOnly color='primary' variant='flat' aria-label='Like'>
      <b>X</b>
    </Button>
  )

  return (
    <>
      <trpc.Provider client={trpcClient} queryClient={queryClient}>
        <QueryClientProvider client={queryClient}>
          <ReactQueryDevtools initialIsOpen={false} />
          <HeroUIProvider navigate={(to) => window.location.href = to}>
              <ValidateUser setLoading={setIsValidateUserLoading} />
              <ToastContainer closeButton={NotificationCloseButton} className='bg-default-100 p-0 rounded-lg shadow-xl' />
              {!network.online && (
                <div className='w-full flex justify-center opacity-90 rounded-b-md items-center bg-danger-500 dark:bg-danger-400 text-white font-bold text-center'>
                  <span className='mr-2'>{t('Network error')}</span>
                  <IconSignalWifiErrorLine className='w-6 h-7' />
                </div>
              )}
              <div>
                <NavBar wsIsConnect={wsIsConnect} />
                {isValidateUserLoading && (
                  <div className='flex fflex-col items-center hh-screen justify-center'>
                    <CircularProgress size='lg' />
                  </div>
                )}
                {!isValidateUserLoading && (
                  <div className='p-2 mt-3 lg:mt-5'>
                    <Outlet />
                  </div>
                )}
              </div>
          </HeroUIProvider>
        </QueryClientProvider>
      </trpc.Provider>
    </>
  )
}

export default App
