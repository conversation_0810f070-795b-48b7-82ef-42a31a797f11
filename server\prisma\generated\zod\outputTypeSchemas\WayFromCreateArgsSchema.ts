import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayFromIncludeSchema } from '../inputTypeSchemas/WayFromIncludeSchema'
import { WayFromCreateInputSchema } from '../inputTypeSchemas/WayFromCreateInputSchema'
import { WayFromUncheckedCreateInputSchema } from '../inputTypeSchemas/WayFromUncheckedCreateInputSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const WayFromSelectSchema: z.ZodType<Prisma.WayFromSelect> = z.object({
  id: z.boolean().optional(),
  geometa: z.boolean().optional(),
  date: z.boolean().optional(),
  lat: z.boolean().optional(),
  lon: z.boolean().optional(),
  caseId: z.boolean().optional(),
  comment: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
}).strict()

export const WayFromCreateArgsSchema: z.ZodType<Prisma.WayFromCreateArgs> = z.object({
  select: WayFromSelectSchema.optional(),
  include: z.lazy(() => WayFromIncludeSchema).optional(),
  data: z.union([ WayFromCreateInputSchema,WayFromUncheckedCreateInputSchema ]),
}).strict() ;

export default WayFromCreateArgsSchema;
