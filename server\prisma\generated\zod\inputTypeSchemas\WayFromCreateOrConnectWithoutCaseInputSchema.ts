import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayFromWhereUniqueInputSchema } from './WayFromWhereUniqueInputSchema';
import { WayFromCreateWithoutCaseInputSchema } from './WayFromCreateWithoutCaseInputSchema';
import { WayFromUncheckedCreateWithoutCaseInputSchema } from './WayFromUncheckedCreateWithoutCaseInputSchema';

export const WayFromCreateOrConnectWithoutCaseInputSchema: z.ZodType<Prisma.WayFromCreateOrConnectWithoutCaseInput> = z.object({
  where: z.lazy(() => WayFromWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => WayFromCreateWithoutCaseInputSchema),z.lazy(() => WayFromUncheckedCreateWithoutCaseInputSchema) ]),
}).strict();

export default WayFromCreateOrConnectWithoutCaseInputSchema;
