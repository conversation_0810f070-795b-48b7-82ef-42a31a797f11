import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsIncludeSchema } from '../inputTypeSchemas/NotificationSettingsIncludeSchema'
import { NotificationSettingsWhereUniqueInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereUniqueInputSchema'
import { NotificationSettingsCreateInputSchema } from '../inputTypeSchemas/NotificationSettingsCreateInputSchema'
import { NotificationSettingsUncheckedCreateInputSchema } from '../inputTypeSchemas/NotificationSettingsUncheckedCreateInputSchema'
import { NotificationSettingsUpdateInputSchema } from '../inputTypeSchemas/NotificationSettingsUpdateInputSchema'
import { NotificationSettingsUncheckedUpdateInputSchema } from '../inputTypeSchemas/NotificationSettingsUncheckedUpdateInputSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const NotificationSettingsSelectSchema: z.ZodType<Prisma.NotificationSettingsSelect> = z.object({
  id: z.boolean().optional(),
  userId: z.boolean().optional(),
  method: z.boolean().optional(),
  active: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const NotificationSettingsUpsertArgsSchema: z.ZodType<Prisma.NotificationSettingsUpsertArgs> = z.object({
  select: NotificationSettingsSelectSchema.optional(),
  include: z.lazy(() => NotificationSettingsIncludeSchema).optional(),
  where: NotificationSettingsWhereUniqueInputSchema,
  create: z.union([ NotificationSettingsCreateInputSchema,NotificationSettingsUncheckedCreateInputSchema ]),
  update: z.union([ NotificationSettingsUpdateInputSchema,NotificationSettingsUncheckedUpdateInputSchema ]),
}).strict() ;

export default NotificationSettingsUpsertArgsSchema;
