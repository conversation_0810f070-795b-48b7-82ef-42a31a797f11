import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayToIncludeSchema } from '../inputTypeSchemas/WayToIncludeSchema'
import { WayToWhereInputSchema } from '../inputTypeSchemas/WayToWhereInputSchema'
import { WayToOrderByWithRelationInputSchema } from '../inputTypeSchemas/WayToOrderByWithRelationInputSchema'
import { WayToWhereUniqueInputSchema } from '../inputTypeSchemas/WayToWhereUniqueInputSchema'
import { WayToScalarFieldEnumSchema } from '../inputTypeSchemas/WayToScalarFieldEnumSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const WayToSelectSchema: z.ZodType<Prisma.WayToSelect> = z.object({
  id: z.boolean().optional(),
  geometa: z.boolean().optional(),
  date: z.boolean().optional(),
  lat: z.boolean().optional(),
  lon: z.boolean().optional(),
  caseId: z.boolean().optional(),
  comment: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
}).strict()

export const WayToFindFirstArgsSchema: z.ZodType<Prisma.WayToFindFirstArgs> = z.object({
  select: WayToSelectSchema.optional(),
  include: z.lazy(() => WayToIncludeSchema).optional(),
  where: WayToWhereInputSchema.optional(),
  orderBy: z.union([ WayToOrderByWithRelationInputSchema.array(),WayToOrderByWithRelationInputSchema ]).optional(),
  cursor: WayToWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ WayToScalarFieldEnumSchema,WayToScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export default WayToFindFirstArgsSchema;
