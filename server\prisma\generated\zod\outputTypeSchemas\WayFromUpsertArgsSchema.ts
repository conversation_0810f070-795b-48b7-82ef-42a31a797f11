import { z } from 'zod';
import type { <PERSON>risma } from '@prisma/client';
import { WayFromIncludeSchema } from '../inputTypeSchemas/WayFromIncludeSchema'
import { WayFromWhereUniqueInputSchema } from '../inputTypeSchemas/WayFromWhereUniqueInputSchema'
import { WayFromCreateInputSchema } from '../inputTypeSchemas/WayFromCreateInputSchema'
import { WayFromUncheckedCreateInputSchema } from '../inputTypeSchemas/WayFromUncheckedCreateInputSchema'
import { WayFromUpdateInputSchema } from '../inputTypeSchemas/WayFromUpdateInputSchema'
import { WayFromUncheckedUpdateInputSchema } from '../inputTypeSchemas/WayFromUncheckedUpdateInputSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const WayFromSelectSchema: z.ZodType<Prisma.WayFromSelect> = z.object({
  id: z.boolean().optional(),
  geometa: z.boolean().optional(),
  date: z.boolean().optional(),
  lat: z.boolean().optional(),
  lon: z.boolean().optional(),
  caseId: z.boolean().optional(),
  comment: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
}).strict()

export const WayFromUpsertArgsSchema: z.ZodType<Prisma.WayFromUpsertArgs> = z.object({
  select: WayFromSelectSchema.optional(),
  include: z.lazy(() => WayFromIncludeSchema).optional(),
  where: WayFromWhereUniqueInputSchema,
  create: z.union([ WayFromCreateInputSchema,WayFromUncheckedCreateInputSchema ]),
  update: z.union([ WayFromUpdateInputSchema,WayFromUncheckedUpdateInputSchema ]),
}).strict() ;

export default WayFromUpsertArgsSchema;
