import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayMiddleCreateManyInputSchema } from '../inputTypeSchemas/WayMiddleCreateManyInputSchema'

export const WayMiddleCreateManyArgsSchema: z.ZodType<Prisma.WayMiddleCreateManyArgs> = z.object({
  data: z.union([ WayMiddleCreateManyInputSchema,WayMiddleCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export default WayMiddleCreateManyArgsSchema;
