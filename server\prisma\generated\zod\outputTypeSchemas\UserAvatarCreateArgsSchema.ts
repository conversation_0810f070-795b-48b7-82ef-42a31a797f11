import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarIncludeSchema } from '../inputTypeSchemas/UserAvatarIncludeSchema'
import { UserAvatarCreateInputSchema } from '../inputTypeSchemas/UserAvatarCreateInputSchema'
import { UserAvatarUncheckedCreateInputSchema } from '../inputTypeSchemas/UserAvatarUncheckedCreateInputSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserAvatarSelectSchema: z.ZodType<Prisma.UserAvatarSelect> = z.object({
  id: z.boolean().optional(),
  userId: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  base64string: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const UserAvatarCreateArgsSchema: z.ZodType<Prisma.UserAvatarCreateArgs> = z.object({
  select: UserAvatarSelectSchema.optional(),
  include: z.lazy(() => UserAvatarIncludeSchema).optional(),
  data: z.union([ UserAvatarCreateInputSchema,UserAvatarUncheckedCreateInputSchema ]),
}).strict() ;

export default UserAvatarCreateArgsSchema;
