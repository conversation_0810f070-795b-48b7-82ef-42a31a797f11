import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAuthenticationLogIncludeSchema } from '../inputTypeSchemas/UserAuthenticationLogIncludeSchema'
import { UserAuthenticationLogWhereUniqueInputSchema } from '../inputTypeSchemas/UserAuthenticationLogWhereUniqueInputSchema'
import { UserAuthenticationLogCreateInputSchema } from '../inputTypeSchemas/UserAuthenticationLogCreateInputSchema'
import { UserAuthenticationLogUncheckedCreateInputSchema } from '../inputTypeSchemas/UserAuthenticationLogUncheckedCreateInputSchema'
import { UserAuthenticationLogUpdateInputSchema } from '../inputTypeSchemas/UserAuthenticationLogUpdateInputSchema'
import { UserAuthenticationLogUncheckedUpdateInputSchema } from '../inputTypeSchemas/UserAuthenticationLogUncheckedUpdateInputSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserAuthenticationLogSelectSchema: z.ZodType<Prisma.UserAuthenticationLogSelect> = z.object({
  id: z.boolean().optional(),
  user_id: z.boolean().optional(),
  ipAddress: z.boolean().optional(),
  userAgent: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const UserAuthenticationLogUpsertArgsSchema: z.ZodType<Prisma.UserAuthenticationLogUpsertArgs> = z.object({
  select: UserAuthenticationLogSelectSchema.optional(),
  include: z.lazy(() => UserAuthenticationLogIncludeSchema).optional(),
  where: UserAuthenticationLogWhereUniqueInputSchema,
  create: z.union([ UserAuthenticationLogCreateInputSchema,UserAuthenticationLogUncheckedCreateInputSchema ]),
  update: z.union([ UserAuthenticationLogUpdateInputSchema,UserAuthenticationLogUncheckedUpdateInputSchema ]),
}).strict() ;

export default UserAuthenticationLogUpsertArgsSchema;
