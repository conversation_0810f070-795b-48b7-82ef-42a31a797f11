import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayMiddleWhereUniqueInputSchema } from './WayMiddleWhereUniqueInputSchema';
import { WayMiddleUpdateWithoutCaseInputSchema } from './WayMiddleUpdateWithoutCaseInputSchema';
import { WayMiddleUncheckedUpdateWithoutCaseInputSchema } from './WayMiddleUncheckedUpdateWithoutCaseInputSchema';
import { WayMiddleCreateWithoutCaseInputSchema } from './WayMiddleCreateWithoutCaseInputSchema';
import { WayMiddleUncheckedCreateWithoutCaseInputSchema } from './WayMiddleUncheckedCreateWithoutCaseInputSchema';

export const WayMiddleUpsertWithWhereUniqueWithoutCaseInputSchema: z.ZodType<Prisma.WayMiddleUpsertWithWhereUniqueWithoutCaseInput> = z.object({
  where: z.lazy(() => WayMiddleWhereUniqueInputSchema),
  update: z.union([ z.lazy(() => WayMiddleUpdateWithoutCaseInputSchema),z.lazy(() => WayMiddleUncheckedUpdateWithoutCaseInputSchema) ]),
  create: z.union([ z.lazy(() => WayMiddleCreateWithoutCaseInputSchema),z.lazy(() => WayMiddleUncheckedCreateWithoutCaseInputSchema) ]),
}).strict();

export default WayMiddleUpsertWithWhereUniqueWithoutCaseInputSchema;
