import { z } from 'zod';
import type { Prisma } from '@prisma/client';

export const UserCountOutputTypeSelectSchema: z.ZodType<Prisma.UserCountOutputTypeSelect> = z.object({
  authoredCases: z.boolean().optional(),
  favorites: z.boolean().optional(),
  accounts: z.boolean().optional(),
  receivedMessages: z.boolean().optional(),
  sentMessages: z.boolean().optional(),
  sendedNotifications: z.boolean().optional(),
  notifications: z.boolean().optional(),
  notificationSettings: z.boolean().optional(),
  auth_session: z.boolean().optional(),
  userAuthenticationLog: z.boolean().optional(),
  sendedRatings: z.boolean().optional(),
  ratings: z.boolean().optional(),
  cases: z.boolean().optional(),
}).strict();

export default UserCountOutputTypeSelectSchema;
