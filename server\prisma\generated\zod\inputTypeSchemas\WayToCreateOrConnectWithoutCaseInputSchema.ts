import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayToWhereUniqueInputSchema } from './WayToWhereUniqueInputSchema';
import { WayToCreateWithoutCaseInputSchema } from './WayToCreateWithoutCaseInputSchema';
import { WayToUncheckedCreateWithoutCaseInputSchema } from './WayToUncheckedCreateWithoutCaseInputSchema';

export const WayToCreateOrConnectWithoutCaseInputSchema: z.ZodType<Prisma.WayToCreateOrConnectWithoutCaseInput> = z.object({
  where: z.lazy(() => WayToWhereUniqueInputSchema),
  create: z.union([ z.lazy(() => WayToCreateWithoutCaseInputSchema),z.lazy(() => WayToUncheckedCreateWithoutCaseInputSchema) ]),
}).strict();

export default WayToCreateOrConnectWithoutCaseInputSchema;
