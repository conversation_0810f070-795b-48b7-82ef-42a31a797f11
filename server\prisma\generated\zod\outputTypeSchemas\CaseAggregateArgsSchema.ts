import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { CaseWhereInputSchema } from '../inputTypeSchemas/CaseWhereInputSchema'
import { CaseOrderByWithRelationInputSchema } from '../inputTypeSchemas/CaseOrderByWithRelationInputSchema'
import { CaseWhereUniqueInputSchema } from '../inputTypeSchemas/CaseWhereUniqueInputSchema'

export const CaseAggregateArgsSchema: z.ZodType<Prisma.CaseAggregateArgs> = z.object({
  where: CaseWhereInputSchema.optional(),
  orderBy: z.union([ CaseOrderByWithRelationInputSchema.array(),CaseOrderByWithRelationInputSchema ]).optional(),
  cursor: CaseWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default CaseAggregateArgsSchema;
