import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayFromWhereInputSchema } from './WayFromWhereInputSchema';
import { WayFromUpdateWithoutCaseInputSchema } from './WayFromUpdateWithoutCaseInputSchema';
import { WayFromUncheckedUpdateWithoutCaseInputSchema } from './WayFromUncheckedUpdateWithoutCaseInputSchema';

export const WayFromUpdateToOneWithWhereWithoutCaseInputSchema: z.ZodType<Prisma.WayFromUpdateToOneWithWhereWithoutCaseInput> = z.object({
  where: z.lazy(() => WayFromWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => WayFromUpdateWithoutCaseInputSchema),z.lazy(() => WayFromUncheckedUpdateWithoutCaseInputSchema) ]),
}).strict();

export default WayFromUpdateToOneWithWhereWithoutCaseInputSchema;
