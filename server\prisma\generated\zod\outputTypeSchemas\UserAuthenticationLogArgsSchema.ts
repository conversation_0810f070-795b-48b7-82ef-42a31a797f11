import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAuthenticationLogSelectSchema } from '../inputTypeSchemas/UserAuthenticationLogSelectSchema';
import { UserAuthenticationLogIncludeSchema } from '../inputTypeSchemas/UserAuthenticationLogIncludeSchema';

export const UserAuthenticationLogArgsSchema: z.ZodType<Prisma.UserAuthenticationLogDefaultArgs> = z.object({
  select: z.lazy(() => UserAuthenticationLogSelectSchema).optional(),
  include: z.lazy(() => UserAuthenticationLogIncludeSchema).optional(),
}).strict();

export default UserAuthenticationLogArgsSchema;
