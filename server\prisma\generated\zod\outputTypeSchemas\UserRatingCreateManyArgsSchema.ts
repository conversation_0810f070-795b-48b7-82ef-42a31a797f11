import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserRatingCreateManyInputSchema } from '../inputTypeSchemas/UserRatingCreateManyInputSchema'

export const UserRatingCreateManyArgsSchema: z.ZodType<Prisma.UserRatingCreateManyArgs> = z.object({
  data: z.union([ UserRatingCreateManyInputSchema,UserRatingCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export default UserRatingCreateManyArgsSchema;
