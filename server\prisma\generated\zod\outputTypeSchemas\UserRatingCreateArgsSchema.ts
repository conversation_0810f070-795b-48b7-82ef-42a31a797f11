import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserRatingIncludeSchema } from '../inputTypeSchemas/UserRatingIncludeSchema'
import { UserRatingCreateInputSchema } from '../inputTypeSchemas/UserRatingCreateInputSchema'
import { UserRatingUncheckedCreateInputSchema } from '../inputTypeSchemas/UserRatingUncheckedCreateInputSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserRatingSelectSchema: z.ZodType<Prisma.UserRatingSelect> = z.object({
  id: z.boolean().optional(),
  rating: z.boolean().optional(),
  comment: z.boolean().optional(),
  userId: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  senderId: z.boolean().optional(),
  caseId: z.boolean().optional(),
  confirm: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
  sender: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const UserRatingCreateArgsSchema: z.ZodType<Prisma.UserRatingCreateArgs> = z.object({
  select: UserRatingSelectSchema.optional(),
  include: z.lazy(() => UserRatingIncludeSchema).optional(),
  data: z.union([ UserRatingCreateInputSchema,UserRatingUncheckedCreateInputSchema ]),
}).strict() ;

export default UserRatingCreateArgsSchema;
