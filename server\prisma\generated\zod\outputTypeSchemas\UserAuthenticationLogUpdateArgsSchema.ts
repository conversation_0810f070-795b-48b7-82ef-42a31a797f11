import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAuthenticationLogIncludeSchema } from '../inputTypeSchemas/UserAuthenticationLogIncludeSchema'
import { UserAuthenticationLogUpdateInputSchema } from '../inputTypeSchemas/UserAuthenticationLogUpdateInputSchema'
import { UserAuthenticationLogUncheckedUpdateInputSchema } from '../inputTypeSchemas/UserAuthenticationLogUncheckedUpdateInputSchema'
import { UserAuthenticationLogWhereUniqueInputSchema } from '../inputTypeSchemas/UserAuthenticationLogWhereUniqueInputSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserAuthenticationLogSelectSchema: z.ZodType<Prisma.UserAuthenticationLogSelect> = z.object({
  id: z.boolean().optional(),
  user_id: z.boolean().optional(),
  ipAddress: z.boolean().optional(),
  userAgent: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const UserAuthenticationLogUpdateArgsSchema: z.ZodType<Prisma.UserAuthenticationLogUpdateArgs> = z.object({
  select: UserAuthenticationLogSelectSchema.optional(),
  include: z.lazy(() => UserAuthenticationLogIncludeSchema).optional(),
  data: z.union([ UserAuthenticationLogUpdateInputSchema,UserAuthenticationLogUncheckedUpdateInputSchema ]),
  where: UserAuthenticationLogWhereUniqueInputSchema,
}).strict() ;

export default UserAuthenticationLogUpdateArgsSchema;
