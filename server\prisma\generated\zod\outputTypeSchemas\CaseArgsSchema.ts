import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { CaseSelectSchema } from '../inputTypeSchemas/CaseSelectSchema';
import { CaseIncludeSchema } from '../inputTypeSchemas/CaseIncludeSchema';

export const CaseArgsSchema: z.ZodType<Prisma.CaseDefaultArgs> = z.object({
  select: z.lazy(() => CaseSelectSchema).optional(),
  include: z.lazy(() => CaseIncludeSchema).optional(),
}).strict();

export default CaseArgsSchema;
