import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsUpdateManyMutationInputSchema } from '../inputTypeSchemas/NotificationSettingsUpdateManyMutationInputSchema'
import { NotificationSettingsUncheckedUpdateManyInputSchema } from '../inputTypeSchemas/NotificationSettingsUncheckedUpdateManyInputSchema'
import { NotificationSettingsWhereInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereInputSchema'

export const NotificationSettingsUpdateManyArgsSchema: z.ZodType<Prisma.NotificationSettingsUpdateManyArgs> = z.object({
  data: z.union([ NotificationSettingsUpdateManyMutationInputSchema,NotificationSettingsUncheckedUpdateManyInputSchema ]),
  where: NotificationSettingsWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default NotificationSettingsUpdateManyArgsSchema;
