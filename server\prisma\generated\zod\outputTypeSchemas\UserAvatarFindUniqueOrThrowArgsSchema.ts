import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarIncludeSchema } from '../inputTypeSchemas/UserAvatarIncludeSchema'
import { UserAvatarWhereUniqueInputSchema } from '../inputTypeSchemas/UserAvatarWhereUniqueInputSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserAvatarSelectSchema: z.ZodType<Prisma.UserAvatarSelect> = z.object({
  id: z.boolean().optional(),
  userId: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  base64string: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const UserAvatarFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.UserAvatarFindUniqueOrThrowArgs> = z.object({
  select: UserAvatarSelectSchema.optional(),
  include: z.lazy(() => UserAvatarIncludeSchema).optional(),
  where: UserAvatarWhereUniqueInputSchema,
}).strict() ;

export default UserAvatarFindUniqueOrThrowArgsSchema;
