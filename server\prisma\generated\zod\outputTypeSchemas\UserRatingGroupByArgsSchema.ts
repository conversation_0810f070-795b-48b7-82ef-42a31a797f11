import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserRatingWhereInputSchema } from '../inputTypeSchemas/UserRatingWhereInputSchema'
import { UserRatingOrderByWithAggregationInputSchema } from '../inputTypeSchemas/UserRatingOrderByWithAggregationInputSchema'
import { UserRatingScalarFieldEnumSchema } from '../inputTypeSchemas/UserRatingScalarFieldEnumSchema'
import { UserRatingScalarWhereWithAggregatesInputSchema } from '../inputTypeSchemas/UserRatingScalarWhereWithAggregatesInputSchema'

export const UserRatingGroupByArgsSchema: z.ZodType<Prisma.UserRatingGroupByArgs> = z.object({
  where: UserRatingWhereInputSchema.optional(),
  orderBy: z.union([ UserRatingOrderByWithAggregationInputSchema.array(),UserRatingOrderByWithAggregationInputSchema ]).optional(),
  by: UserRatingScalarFieldEnumSchema.array(),
  having: UserRatingScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default UserRatingGroupByArgsSchema;
