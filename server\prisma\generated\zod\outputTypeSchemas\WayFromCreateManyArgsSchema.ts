import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayFromCreateManyInputSchema } from '../inputTypeSchemas/WayFromCreateManyInputSchema'

export const WayFromCreateManyArgsSchema: z.ZodType<Prisma.WayFromCreateManyArgs> = z.object({
  data: z.union([ WayFromCreateManyInputSchema,WayFromCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export default WayFromCreateManyArgsSchema;
