import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayToWhereInputSchema } from '../inputTypeSchemas/WayToWhereInputSchema'
import { WayToOrderByWithRelationInputSchema } from '../inputTypeSchemas/WayToOrderByWithRelationInputSchema'
import { WayToWhereUniqueInputSchema } from '../inputTypeSchemas/WayToWhereUniqueInputSchema'

export const WayToAggregateArgsSchema: z.ZodType<Prisma.WayToAggregateArgs> = z.object({
  where: WayToWhereInputSchema.optional(),
  orderBy: z.union([ WayToOrderByWithRelationInputSchema.array(),WayToOrderByWithRelationInputSchema ]).optional(),
  cursor: WayToWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default WayToAggregateArgsSchema;
