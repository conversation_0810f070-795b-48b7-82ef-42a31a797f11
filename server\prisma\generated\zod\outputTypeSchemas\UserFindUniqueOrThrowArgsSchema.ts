import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserIncludeSchema } from '../inputTypeSchemas/UserIncludeSchema'
import { UserWhereUniqueInputSchema } from '../inputTypeSchemas/UserWhereUniqueInputSchema'
import { CaseFindManyArgsSchema } from "../outputTypeSchemas/CaseFindManyArgsSchema"
import { FavoriteFindManyArgsSchema } from "../outputTypeSchemas/FavoriteFindManyArgsSchema"
import { AccountFindManyArgsSchema } from "../outputTypeSchemas/AccountFindManyArgsSchema"
import { MessageFindManyArgsSchema } from "../outputTypeSchemas/MessageFindManyArgsSchema"
import { NotificationFindManyArgsSchema } from "../outputTypeSchemas/NotificationFindManyArgsSchema"
import { NotificationSettingsFindManyArgsSchema } from "../outputTypeSchemas/NotificationSettingsFindManyArgsSchema"
import { SessionFindManyArgsSchema } from "../outputTypeSchemas/SessionFindManyArgsSchema"
import { UserAuthenticationLogFindManyArgsSchema } from "../outputTypeSchemas/UserAuthenticationLogFindManyArgsSchema"
import { UserAvatarArgsSchema } from "../outputTypeSchemas/UserAvatarArgsSchema"
import { UserRatingFindManyArgsSchema } from "../outputTypeSchemas/UserRatingFindManyArgsSchema"
import { UserCountOutputTypeArgsSchema } from "../outputTypeSchemas/UserCountOutputTypeArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserSelectSchema: z.ZodType<Prisma.UserSelect> = z.object({
  id: z.boolean().optional(),
  username: z.boolean().optional(),
  email: z.boolean().optional(),
  emailVerified: z.boolean().optional(),
  image: z.boolean().optional(),
  name: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  about_me: z.boolean().optional(),
  phone: z.boolean().optional(),
  blocked: z.boolean().optional(),
  confirmed: z.boolean().optional(),
  role: z.boolean().optional(),
  language: z.boolean().optional(),
  authoredCases: z.union([z.boolean(),z.lazy(() => CaseFindManyArgsSchema)]).optional(),
  favorites: z.union([z.boolean(),z.lazy(() => FavoriteFindManyArgsSchema)]).optional(),
  accounts: z.union([z.boolean(),z.lazy(() => AccountFindManyArgsSchema)]).optional(),
  receivedMessages: z.union([z.boolean(),z.lazy(() => MessageFindManyArgsSchema)]).optional(),
  sentMessages: z.union([z.boolean(),z.lazy(() => MessageFindManyArgsSchema)]).optional(),
  sendedNotifications: z.union([z.boolean(),z.lazy(() => NotificationFindManyArgsSchema)]).optional(),
  notifications: z.union([z.boolean(),z.lazy(() => NotificationFindManyArgsSchema)]).optional(),
  notificationSettings: z.union([z.boolean(),z.lazy(() => NotificationSettingsFindManyArgsSchema)]).optional(),
  auth_session: z.union([z.boolean(),z.lazy(() => SessionFindManyArgsSchema)]).optional(),
  userAuthenticationLog: z.union([z.boolean(),z.lazy(() => UserAuthenticationLogFindManyArgsSchema)]).optional(),
  avatar: z.union([z.boolean(),z.lazy(() => UserAvatarArgsSchema)]).optional(),
  sendedRatings: z.union([z.boolean(),z.lazy(() => UserRatingFindManyArgsSchema)]).optional(),
  ratings: z.union([z.boolean(),z.lazy(() => UserRatingFindManyArgsSchema)]).optional(),
  cases: z.union([z.boolean(),z.lazy(() => CaseFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => UserCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const UserFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.UserFindUniqueOrThrowArgs> = z.object({
  select: UserSelectSchema.optional(),
  include: z.lazy(() => UserIncludeSchema).optional(),
  where: UserWhereUniqueInputSchema,
}).strict() ;

export default UserFindUniqueOrThrowArgsSchema;
