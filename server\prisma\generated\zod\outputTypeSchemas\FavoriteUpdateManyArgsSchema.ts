import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { FavoriteUpdateManyMutationInputSchema } from '../inputTypeSchemas/FavoriteUpdateManyMutationInputSchema'
import { FavoriteUncheckedUpdateManyInputSchema } from '../inputTypeSchemas/FavoriteUncheckedUpdateManyInputSchema'
import { FavoriteWhereInputSchema } from '../inputTypeSchemas/FavoriteWhereInputSchema'

export const FavoriteUpdateManyArgsSchema: z.ZodType<Prisma.FavoriteUpdateManyArgs> = z.object({
  data: z.union([ FavoriteUpdateManyMutationInputSchema,FavoriteUncheckedUpdateManyInputSchema ]),
  where: FavoriteWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default FavoriteUpdateManyArgsSchema;
