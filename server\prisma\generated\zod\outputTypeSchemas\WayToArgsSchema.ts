import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayToSelectSchema } from '../inputTypeSchemas/WayToSelectSchema';
import { WayToIncludeSchema } from '../inputTypeSchemas/WayToIncludeSchema';

export const WayToArgsSchema: z.ZodType<Prisma.WayToDefaultArgs> = z.object({
  select: z.lazy(() => WayToSelectSchema).optional(),
  include: z.lazy(() => WayToIncludeSchema).optional(),
}).strict();

export default WayToArgsSchema;
