import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { CaseCreateManyInputSchema } from '../inputTypeSchemas/CaseCreateManyInputSchema'

export const CaseCreateManyArgsSchema: z.ZodType<Prisma.CaseCreateManyArgs> = z.object({
  data: z.union([ CaseCreateManyInputSchema,CaseCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export default CaseCreateManyArgsSchema;
