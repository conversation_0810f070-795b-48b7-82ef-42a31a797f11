import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { TranslationCreateInputSchema } from '../inputTypeSchemas/TranslationCreateInputSchema'
import { TranslationUncheckedCreateInputSchema } from '../inputTypeSchemas/TranslationUncheckedCreateInputSchema'
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const TranslationSelectSchema: z.ZodType<Prisma.TranslationSelect> = z.object({
  id: z.boolean().optional(),
  key: z.boolean().optional(),
  value: z.boolean().optional(),
  language: z.boolean().optional(),
  includes: z.boolean().optional(),
}).strict()

export const TranslationCreateArgsSchema: z.ZodType<Prisma.TranslationCreateArgs> = z.object({
  select: TranslationSelectSchema.optional(),
  data: z.union([ TranslationCreateInputSchema,TranslationUncheckedCreateInputSchema ]),
}).strict() ;

export default TranslationCreateArgsSchema;
