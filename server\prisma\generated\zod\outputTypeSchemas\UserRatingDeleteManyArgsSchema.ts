import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserRatingWhereInputSchema } from '../inputTypeSchemas/UserRatingWhereInputSchema'

export const UserRatingDeleteManyArgsSchema: z.ZodType<Prisma.UserRatingDeleteManyArgs> = z.object({
  where: UserRatingWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default UserRatingDeleteManyArgsSchema;
