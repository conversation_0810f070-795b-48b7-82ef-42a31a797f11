import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarIncludeSchema } from '../inputTypeSchemas/UserAvatarIncludeSchema'
import { UserAvatarWhereUniqueInputSchema } from '../inputTypeSchemas/UserAvatarWhereUniqueInputSchema'
import { UserAvatarCreateInputSchema } from '../inputTypeSchemas/UserAvatarCreateInputSchema'
import { UserAvatarUncheckedCreateInputSchema } from '../inputTypeSchemas/UserAvatarUncheckedCreateInputSchema'
import { UserAvatarUpdateInputSchema } from '../inputTypeSchemas/UserAvatarUpdateInputSchema'
import { UserAvatarUncheckedUpdateInputSchema } from '../inputTypeSchemas/UserAvatarUncheckedUpdateInputSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const UserAvatarSelectSchema: z.ZodType<Prisma.UserAvatarSelect> = z.object({
  id: z.boolean().optional(),
  userId: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  updatedAt: z.boolean().optional(),
  base64string: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const UserAvatarUpsertArgsSchema: z.ZodType<Prisma.UserAvatarUpsertArgs> = z.object({
  select: UserAvatarSelectSchema.optional(),
  include: z.lazy(() => UserAvatarIncludeSchema).optional(),
  where: UserAvatarWhereUniqueInputSchema,
  create: z.union([ UserAvatarCreateInputSchema,UserAvatarUncheckedCreateInputSchema ]),
  update: z.union([ UserAvatarUpdateInputSchema,UserAvatarUncheckedUpdateInputSchema ]),
}).strict() ;

export default UserAvatarUpsertArgsSchema;
