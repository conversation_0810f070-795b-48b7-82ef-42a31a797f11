import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { TranslationWhereUniqueInputSchema } from '../inputTypeSchemas/TranslationWhereUniqueInputSchema'
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const TranslationSelectSchema: z.ZodType<Prisma.TranslationSelect> = z.object({
  id: z.boolean().optional(),
  key: z.boolean().optional(),
  value: z.boolean().optional(),
  language: z.boolean().optional(),
  includes: z.boolean().optional(),
}).strict()

export const TranslationFindUniqueOrThrowArgsSchema: z.ZodType<Prisma.TranslationFindUniqueOrThrowArgs> = z.object({
  select: TranslationSelectSchema.optional(),
  where: TranslationWhereUniqueInputSchema,
}).strict() ;

export default TranslationFindUniqueOrThrowArgsSchema;
