import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAvatarWhereInputSchema } from '../inputTypeSchemas/UserAvatarWhereInputSchema'
import { UserAvatarOrderByWithAggregationInputSchema } from '../inputTypeSchemas/UserAvatarOrderByWithAggregationInputSchema'
import { UserAvatarScalarFieldEnumSchema } from '../inputTypeSchemas/UserAvatarScalarFieldEnumSchema'
import { UserAvatarScalarWhereWithAggregatesInputSchema } from '../inputTypeSchemas/UserAvatarScalarWhereWithAggregatesInputSchema'

export const UserAvatarGroupByArgsSchema: z.ZodType<Prisma.UserAvatarGroupByArgs> = z.object({
  where: UserAvatarWhereInputSchema.optional(),
  orderBy: z.union([ UserAvatarOrderByWithAggregationInputSchema.array(),UserAvatarOrderByWithAggregationInputSchema ]).optional(),
  by: UserAvatarScalarFieldEnumSchema.array(),
  having: UserAvatarScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default UserAvatarGroupByArgsSchema;
