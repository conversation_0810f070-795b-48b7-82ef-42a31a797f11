import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayToUpdateManyMutationInputSchema } from '../inputTypeSchemas/WayToUpdateManyMutationInputSchema'
import { WayToUncheckedUpdateManyInputSchema } from '../inputTypeSchemas/WayToUncheckedUpdateManyInputSchema'
import { WayToWhereInputSchema } from '../inputTypeSchemas/WayToWhereInputSchema'

export const WayToUpdateManyArgsSchema: z.ZodType<Prisma.WayToUpdateManyArgs> = z.object({
  data: z.union([ WayToUpdateManyMutationInputSchema,WayToUncheckedUpdateManyInputSchema ]),
  where: WayToWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default WayToUpdateManyArgsSchema;
