import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAuthenticationLogUpdateManyMutationInputSchema } from '../inputTypeSchemas/UserAuthenticationLogUpdateManyMutationInputSchema'
import { UserAuthenticationLogUncheckedUpdateManyInputSchema } from '../inputTypeSchemas/UserAuthenticationLogUncheckedUpdateManyInputSchema'
import { UserAuthenticationLogWhereInputSchema } from '../inputTypeSchemas/UserAuthenticationLogWhereInputSchema'

export const UserAuthenticationLogUpdateManyArgsSchema: z.ZodType<Prisma.UserAuthenticationLogUpdateManyArgs> = z.object({
  data: z.union([ UserAuthenticationLogUpdateManyMutationInputSchema,UserAuthenticationLogUncheckedUpdateManyInputSchema ]),
  where: UserAuthenticationLogWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default UserAuthenticationLogUpdateManyArgsSchema;
