import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsSelectSchema } from '../inputTypeSchemas/NotificationSettingsSelectSchema';
import { NotificationSettingsIncludeSchema } from '../inputTypeSchemas/NotificationSettingsIncludeSchema';

export const NotificationSettingsArgsSchema: z.ZodType<Prisma.NotificationSettingsDefaultArgs> = z.object({
  select: z.lazy(() => NotificationSettingsSelectSchema).optional(),
  include: z.lazy(() => NotificationSettingsIncludeSchema).optional(),
}).strict();

export default NotificationSettingsArgsSchema;
