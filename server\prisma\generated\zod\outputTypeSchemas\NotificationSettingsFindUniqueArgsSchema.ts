import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsIncludeSchema } from '../inputTypeSchemas/NotificationSettingsIncludeSchema'
import { NotificationSettingsWhereUniqueInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereUniqueInputSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const NotificationSettingsSelectSchema: z.ZodType<Prisma.NotificationSettingsSelect> = z.object({
  id: z.boolean().optional(),
  userId: z.boolean().optional(),
  method: z.boolean().optional(),
  active: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const NotificationSettingsFindUniqueArgsSchema: z.ZodType<Prisma.NotificationSettingsFindUniqueArgs> = z.object({
  select: NotificationSettingsSelectSchema.optional(),
  include: z.lazy(() => NotificationSettingsIncludeSchema).optional(),
  where: NotificationSettingsWhereUniqueInputSchema,
}).strict() ;

export default NotificationSettingsFindUniqueArgsSchema;
