import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsWhereInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereInputSchema'

export const NotificationSettingsDeleteManyArgsSchema: z.ZodType<Prisma.NotificationSettingsDeleteManyArgs> = z.object({
  where: NotificationSettingsWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default NotificationSettingsDeleteManyArgsSchema;
