import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserUpdateWithoutUserAuthenticationLogInputSchema } from './UserUpdateWithoutUserAuthenticationLogInputSchema';
import { UserUncheckedUpdateWithoutUserAuthenticationLogInputSchema } from './UserUncheckedUpdateWithoutUserAuthenticationLogInputSchema';
import { UserCreateWithoutUserAuthenticationLogInputSchema } from './UserCreateWithoutUserAuthenticationLogInputSchema';
import { UserUncheckedCreateWithoutUserAuthenticationLogInputSchema } from './UserUncheckedCreateWithoutUserAuthenticationLogInputSchema';
import { UserWhereInputSchema } from './UserWhereInputSchema';

export const UserUpsertWithoutUserAuthenticationLogInputSchema: z.ZodType<Prisma.UserUpsertWithoutUserAuthenticationLogInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutUserAuthenticationLogInputSchema),z.lazy(() => UserUncheckedUpdateWithoutUserAuthenticationLogInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutUserAuthenticationLogInputSchema),z.lazy(() => UserUncheckedCreateWithoutUserAuthenticationLogInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export default UserUpsertWithoutUserAuthenticationLogInputSchema;
