import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsWhereInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereInputSchema'
import { NotificationSettingsOrderByWithRelationInputSchema } from '../inputTypeSchemas/NotificationSettingsOrderByWithRelationInputSchema'
import { NotificationSettingsWhereUniqueInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereUniqueInputSchema'

export const NotificationSettingsAggregateArgsSchema: z.ZodType<Prisma.NotificationSettingsAggregateArgs> = z.object({
  where: NotificationSettingsWhereInputSchema.optional(),
  orderBy: z.union([ NotificationSettingsOrderByWithRelationInputSchema.array(),NotificationSettingsOrderByWithRelationInputSchema ]).optional(),
  cursor: NotificationSettingsWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default NotificationSettingsAggregateArgsSchema;
