import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayMiddleUpdateManyMutationInputSchema } from '../inputTypeSchemas/WayMiddleUpdateManyMutationInputSchema'
import { WayMiddleUncheckedUpdateManyInputSchema } from '../inputTypeSchemas/WayMiddleUncheckedUpdateManyInputSchema'
import { WayMiddleWhereInputSchema } from '../inputTypeSchemas/WayMiddleWhereInputSchema'

export const WayMiddleUpdateManyArgsSchema: z.ZodType<Prisma.WayMiddleUpdateManyArgs> = z.object({
  data: z.union([ WayMiddleUpdateManyMutationInputSchema,WayMiddleUncheckedUpdateManyInputSchema ]),
  where: WayMiddleWhereInputSchema.optional(),
  limit: z.number().optional(),
}).strict() ;

export default WayMiddleUpdateManyArgsSchema;
