import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserUpdateWithoutSendedNotificationsInputSchema } from './UserUpdateWithoutSendedNotificationsInputSchema';
import { UserUncheckedUpdateWithoutSendedNotificationsInputSchema } from './UserUncheckedUpdateWithoutSendedNotificationsInputSchema';
import { UserCreateWithoutSendedNotificationsInputSchema } from './UserCreateWithoutSendedNotificationsInputSchema';
import { UserUncheckedCreateWithoutSendedNotificationsInputSchema } from './UserUncheckedCreateWithoutSendedNotificationsInputSchema';
import { UserWhereInputSchema } from './UserWhereInputSchema';

export const UserUpsertWithoutSendedNotificationsInputSchema: z.ZodType<Prisma.UserUpsertWithoutSendedNotificationsInput> = z.object({
  update: z.union([ z.lazy(() => UserUpdateWithoutSendedNotificationsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutSendedNotificationsInputSchema) ]),
  create: z.union([ z.lazy(() => UserCreateWithoutSendedNotificationsInputSchema),z.lazy(() => UserUncheckedCreateWithoutSendedNotificationsInputSchema) ]),
  where: z.lazy(() => UserWhereInputSchema).optional()
}).strict();

export default UserUpsertWithoutSendedNotificationsInputSchema;
