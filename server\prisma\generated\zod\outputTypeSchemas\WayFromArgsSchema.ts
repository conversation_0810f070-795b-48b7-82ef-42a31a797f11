import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayFromSelectSchema } from '../inputTypeSchemas/WayFromSelectSchema';
import { WayFromIncludeSchema } from '../inputTypeSchemas/WayFromIncludeSchema';

export const WayFromArgsSchema: z.ZodType<Prisma.WayFromDefaultArgs> = z.object({
  select: z.lazy(() => WayFromSelectSchema).optional(),
  include: z.lazy(() => WayFromIncludeSchema).optional(),
}).strict();

export default WayFromArgsSchema;
