import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAuthenticationLogWhereInputSchema } from '../inputTypeSchemas/UserAuthenticationLogWhereInputSchema'
import { UserAuthenticationLogOrderByWithRelationInputSchema } from '../inputTypeSchemas/UserAuthenticationLogOrderByWithRelationInputSchema'
import { UserAuthenticationLogWhereUniqueInputSchema } from '../inputTypeSchemas/UserAuthenticationLogWhereUniqueInputSchema'

export const UserAuthenticationLogAggregateArgsSchema: z.ZodType<Prisma.UserAuthenticationLogAggregateArgs> = z.object({
  where: UserAuthenticationLogWhereInputSchema.optional(),
  orderBy: z.union([ UserAuthenticationLogOrderByWithRelationInputSchema.array(),UserAuthenticationLogOrderByWithRelationInputSchema ]).optional(),
  cursor: UserAuthenticationLogWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default UserAuthenticationLogAggregateArgsSchema;
