import { Prisma } from '@prisma/client';

import { z } from 'zod';
import { JsonNullValueInputSchema } from './JsonNullValueInputSchema';
import { InputJsonValueSchema } from './InputJsonValueSchema';
import { isValidDecimalInput } from './isValidDecimalInput';
import { DecimalJsLikeSchema } from './DecimalJsLikeSchema';

export const WayToCreateWithoutCaseInputSchema: z.ZodType<Prisma.WayToCreateWithoutCaseInput> = z.object({
  geometa: z.union([ z.lazy(() => JsonNullValueInputSchema),InputJsonValueSchema ]),
  date: z.coerce.date().optional().nullable(),
  lat: z.union([z.number(),z.string(),z.instanceof(Prisma.Decimal),DecimalJsLikeSchema,]).refine((v) => isValidDecimalInput(v), { message: 'Must be a Decimal' }),
  lon: z.union([z.number(),z.string(),z.instanceof(Prisma.Decimal),DecimalJsLikeSchema,]).refine((v) => isValidDecimalInput(v), { message: 'Must be a Decimal' }),
  comment: z.string().optional().nullable()
}).strict();

export default WayToCreateWithoutCaseInputSchema;
