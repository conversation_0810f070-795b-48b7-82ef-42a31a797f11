import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayToCreateManyInputSchema } from '../inputTypeSchemas/WayToCreateManyInputSchema'

export const WayToCreateManyArgsSchema: z.ZodType<Prisma.WayToCreateManyArgs> = z.object({
  data: z.union([ WayToCreateManyInputSchema,WayToCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export default WayToCreateManyArgsSchema;
