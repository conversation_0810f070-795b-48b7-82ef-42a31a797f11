import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsWhereInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereInputSchema'
import { NotificationSettingsOrderByWithAggregationInputSchema } from '../inputTypeSchemas/NotificationSettingsOrderByWithAggregationInputSchema'
import { NotificationSettingsScalarFieldEnumSchema } from '../inputTypeSchemas/NotificationSettingsScalarFieldEnumSchema'
import { NotificationSettingsScalarWhereWithAggregatesInputSchema } from '../inputTypeSchemas/NotificationSettingsScalarWhereWithAggregatesInputSchema'

export const NotificationSettingsGroupByArgsSchema: z.ZodType<Prisma.NotificationSettingsGroupByArgs> = z.object({
  where: NotificationSettingsWhereInputSchema.optional(),
  orderBy: z.union([ NotificationSettingsOrderByWithAggregationInputSchema.array(),NotificationSettingsOrderByWithAggregationInputSchema ]).optional(),
  by: NotificationSettingsScalarFieldEnumSchema.array(),
  having: NotificationSettingsScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default NotificationSettingsGroupByArgsSchema;
