import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsIncludeSchema } from '../inputTypeSchemas/NotificationSettingsIncludeSchema'
import { NotificationSettingsWhereInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereInputSchema'
import { NotificationSettingsOrderByWithRelationInputSchema } from '../inputTypeSchemas/NotificationSettingsOrderByWithRelationInputSchema'
import { NotificationSettingsWhereUniqueInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereUniqueInputSchema'
import { NotificationSettingsScalarFieldEnumSchema } from '../inputTypeSchemas/NotificationSettingsScalarFieldEnumSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const NotificationSettingsSelectSchema: z.ZodType<Prisma.NotificationSettingsSelect> = z.object({
  id: z.boolean().optional(),
  userId: z.boolean().optional(),
  method: z.boolean().optional(),
  active: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const NotificationSettingsFindFirstArgsSchema: z.ZodType<Prisma.NotificationSettingsFindFirstArgs> = z.object({
  select: NotificationSettingsSelectSchema.optional(),
  include: z.lazy(() => NotificationSettingsIncludeSchema).optional(),
  where: NotificationSettingsWhereInputSchema.optional(),
  orderBy: z.union([ NotificationSettingsOrderByWithRelationInputSchema.array(),NotificationSettingsOrderByWithRelationInputSchema ]).optional(),
  cursor: NotificationSettingsWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ NotificationSettingsScalarFieldEnumSchema,NotificationSettingsScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export default NotificationSettingsFindFirstArgsSchema;
