import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserRatingWhereInputSchema } from '../inputTypeSchemas/UserRatingWhereInputSchema'
import { UserRatingOrderByWithRelationInputSchema } from '../inputTypeSchemas/UserRatingOrderByWithRelationInputSchema'
import { UserRatingWhereUniqueInputSchema } from '../inputTypeSchemas/UserRatingWhereUniqueInputSchema'

export const UserRatingAggregateArgsSchema: z.ZodType<Prisma.UserRatingAggregateArgs> = z.object({
  where: UserRatingWhereInputSchema.optional(),
  orderBy: z.union([ UserRatingOrderByWithRelationInputSchema.array(),UserRatingOrderByWithRelationInputSchema ]).optional(),
  cursor: UserRatingWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default UserRatingAggregateArgsSchema;
