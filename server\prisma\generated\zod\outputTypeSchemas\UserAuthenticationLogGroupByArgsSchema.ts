import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { UserAuthenticationLogWhereInputSchema } from '../inputTypeSchemas/UserAuthenticationLogWhereInputSchema'
import { UserAuthenticationLogOrderByWithAggregationInputSchema } from '../inputTypeSchemas/UserAuthenticationLogOrderByWithAggregationInputSchema'
import { UserAuthenticationLogScalarFieldEnumSchema } from '../inputTypeSchemas/UserAuthenticationLogScalarFieldEnumSchema'
import { UserAuthenticationLogScalarWhereWithAggregatesInputSchema } from '../inputTypeSchemas/UserAuthenticationLogScalarWhereWithAggregatesInputSchema'

export const UserAuthenticationLogGroupByArgsSchema: z.ZodType<Prisma.UserAuthenticationLogGroupByArgs> = z.object({
  where: UserAuthenticationLogWhereInputSchema.optional(),
  orderBy: z.union([ UserAuthenticationLogOrderByWithAggregationInputSchema.array(),UserAuthenticationLogOrderByWithAggregationInputSchema ]).optional(),
  by: UserAuthenticationLogScalarFieldEnumSchema.array(),
  having: UserAuthenticationLogScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default UserAuthenticationLogGroupByArgsSchema;
