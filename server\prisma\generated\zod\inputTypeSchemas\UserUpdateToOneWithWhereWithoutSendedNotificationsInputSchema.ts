import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { UserWhereInputSchema } from './UserWhereInputSchema';
import { UserUpdateWithoutSendedNotificationsInputSchema } from './UserUpdateWithoutSendedNotificationsInputSchema';
import { UserUncheckedUpdateWithoutSendedNotificationsInputSchema } from './UserUncheckedUpdateWithoutSendedNotificationsInputSchema';

export const UserUpdateToOneWithWhereWithoutSendedNotificationsInputSchema: z.ZodType<Prisma.UserUpdateToOneWithWhereWithoutSendedNotificationsInput> = z.object({
  where: z.lazy(() => UserWhereInputSchema).optional(),
  data: z.union([ z.lazy(() => UserUpdateWithoutSendedNotificationsInputSchema),z.lazy(() => UserUncheckedUpdateWithoutSendedNotificationsInputSchema) ]),
}).strict();

export default UserUpdateToOneWithWhereWithoutSendedNotificationsInputSchema;
