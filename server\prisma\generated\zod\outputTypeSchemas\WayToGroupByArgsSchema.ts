import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayToWhereInputSchema } from '../inputTypeSchemas/WayToWhereInputSchema'
import { WayToOrderByWithAggregationInputSchema } from '../inputTypeSchemas/WayToOrderByWithAggregationInputSchema'
import { WayToScalarFieldEnumSchema } from '../inputTypeSchemas/WayToScalarFieldEnumSchema'
import { WayToScalarWhereWithAggregatesInputSchema } from '../inputTypeSchemas/WayToScalarWhereWithAggregatesInputSchema'

export const WayToGroupByArgsSchema: z.ZodType<Prisma.WayToGroupByArgs> = z.object({
  where: WayToWhereInputSchema.optional(),
  orderBy: z.union([ WayToOrderByWithAggregationInputSchema.array(),WayToOrderByWithAggregationInputSchema ]).optional(),
  by: WayToScalarFieldEnumSchema.array(),
  having: WayToScalarWhereWithAggregatesInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
}).strict() ;

export default WayToGroupByArgsSchema;
