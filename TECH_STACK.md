# Технологический стек проекта TakeNPass

## Обзор архитектуры

Проект TakeNPass представляет собой полнофункциональное веб-приложение для поиска попутчиков, построенное на современном технологическом стеке с использованием TypeScript, React и Node.js.

## Frontend (Клиентская часть)

### Основные технологии

- **React 18.3.1** - Основная библиотека для построения пользовательского интерфейса
- **TypeScript 5.9.2** - Статическая типизация для JavaScript
- **Vite** - Современный инструмент сборки и разработки
- **Tailwind CSS** - Utility-first CSS фреймворк для стилизации

### UI библиотеки и компоненты

- **@heroui/react 2.8.3** - Основная UI библиотека компонентов
- **@radix-ui/react-icons 1.3.2** - Набор иконок
- **@radix-ui/react-slot 1.2.3** - Примитивы для композиции компонентов
- **class-variance-authority 0.7.1** - Утилита для управления вариантами CSS классов
- **clsx 2.1.1** - Утилита для условного объединения CSS классов
- **tailwind-merge 3.3.1** - Объединение Tailwind CSS классов
- **tailwindcss-animate 1.0.7** - Анимации для Tailwind CSS
- **framer-motion 12.23.12** - Библиотека анимаций

### Маршрутизация и состояние

- **@tanstack/react-router 1.131.36** - Type-safe маршрутизация
- **zustand 5.0.8** - Легковесное управление состоянием
- **use-bus 2.5.2** - Event bus для React
- **@uidotdev/usehooks 2.4.1** - Коллекция полезных React хуков

### Работа с данными

- **@tanstack/react-query 5.87.1** - Управление серверным состоянием
- **@tanstack/react-query-devtools 5.87.3** - Инструменты разработчика для React Query
- **@trpc/client 11.5.1** - Type-safe API клиент
- **@trpc/react-query 11.5.1** - Интеграция tRPC с React Query
- **superjson 2.2.2** - Сериализация данных с поддержкой дополнительных типов

### Специализированные библиотеки

- **leaflet 4.2.1** & **react-leaflet 4.2.1** - Интерактивные карты
- **socket.io-client 4.8.1** - WebSocket клиент для real-time коммуникации
- **react-i18next 14.1.3** & **i18next 23.16.8** - Интернационализация
- **react-phone-number-input 3.4.12** - Ввод номеров телефонов
- **@smastrom/react-rating 1.5.0** - Компонент рейтинга
- **react-toastify 11.0.5** - Уведомления
- **dayjs 1.11.18** & **luxon 3.7.2** - Работа с датами
- **query-string 9.2.2** - Парсинг query параметров

### Аутентификация

- **better-auth 1.3.9** - Современная система аутентификации

### Инструменты разработки

- **@vitejs/plugin-react** - Vite плагин для React
- **@tailwindcss/vite** - Vite плагин для Tailwind CSS
- **i18next-scanner** - Сканер для извлечения переводов

## Backend (Серверная часть)

### Основные технологии

- **Node.js** - Серверная платформа JavaScript
- **TypeScript 5.9.2** - Статическая типизация
- **Fastify 5.6.0** - Высокопроизводительный веб-фреймворк
- **ESBuild 0.25.9** - Быстрый бандлер для production сборки

### API и коммуникация

- **@trpc/server 11.5.1** - Type-safe API сервер
- **@fastify/cors 11.1.0** - CORS middleware
- **@fastify/cookie 11.0.2** - Управление cookies
- **fastify-socket.io 5.1.0** - WebSocket интеграция
- **socket.io 4.8.1** - Real-time двунаправленная коммуникация
- **superjson 2.2.2** - Сериализация данных

### База данных

- **@prisma/client 6.15.0** - ORM для работы с базой данных
- **prisma 6.15.0** - Инструменты разработчика Prisma
- **zod-prisma-types 3.2.4** - Генерация Zod схем из Prisma моделей

### Валидация и типизация

- **zod 4.1.5** - Schema валидация с TypeScript поддержкой

### Аутентификация

- **better-auth 1.3.9** - Система аутентификации

### Утилиты

- **ky 1.10.0** - HTTP клиент
- **libphonenumber-js 1.12.15** - Валидация номеров телефонов
- **replace-special-characters 1.2.7** - Замена специальных символов
- **dotenv 17.2.2** - Загрузка переменных окружения
- **tslib 2.8.1** - TypeScript runtime библиотека

### Интернационализация

- **i18next 23.16.8** - Основная библиотека интернационализации
- **i18next-http-middleware 3.8.0** - HTTP middleware для i18next
- **i18next-scanner 4.6.0** - Сканер переводов

### Инструменты разработки

- **tsx 4.20.5** - TypeScript выполнение для разработки
- **vitest 3.2.4** - Фреймворк тестирования
- **@vitest/ui 3.2.4** - UI для тестов
- **npm-run-all 4.1.5** - Параллельное выполнение npm скриптов
- **start-server-and-test 1.15.5** - Утилита для тестирования

## База данных

### ORM и схема

- **Prisma** - Современная ORM с type-safe клиентом
- **MySQL** - Реляционная база данных (настроена в Prisma адаптере)

### Основные модели данных

- **User** - Пользователи системы
- **Case** - Поездки/заявки
- **CaseApplication** - Заявки на участие в поездках
- **WayFrom/WayTo/WayMiddle** - Географические точки маршрута
- **Notification** - Уведомления
- **Message** - Сообщения чата
- **UserRating** - Рейтинги пользователей
- **Favorite** - Избранные поездки
- **NotificationSettings** - Настройки уведомлений
- **UserAvatar** - Аватары пользователей

## Архитектурные особенности

### Type Safety

- Полная типизация от базы данных до frontend через tRPC
- Автогенерация типов из Prisma схемы
- Zod валидация на всех уровнях

### Real-time коммуникация

- WebSocket соединения через Socket.IO
- Мгновенные уведомления
- Real-time чат между пользователями

### Интернационализация

- Поддержка множественных языков (ru, en, es)
- Автоматическое сканирование и генерация переводов
- Server-side и client-side локализация

### Производительность

- Vite для быстрой разработки
- ESBuild для production сборки
- React Query для кэширования данных
- Оптимизированные SQL запросы через Prisma

### Безопасность

- Better Auth для современной аутентификации
- CORS настройки
- Rate limiting
- Валидация всех входящих данных

### Геолокация

- Leaflet для интерактивных карт
- Поддержка множественных геосервисов (LocationIQ, Geoapify)
- Расчет расстояний и радиуса поиска

## Среда разработки

### Инструменты

- **TypeScript** - Статическая типизация
- **ESLint** - Линтинг кода
- **Prettier** - Форматирование кода
- **Vitest** - Тестирование
- **Git** - Контроль версий

### Скрипты разработки

- `npm run dev` - Запуск development сервера
- `npm run build` - Production сборка
- `npm run test` - Запуск тестов
- `npm run type-check` - Проверка типов

## Развертывание

### Production готовность

- Оптимизированная сборка через ESBuild
- Минификация и tree-shaking
- Переменные окружения для конфигурации
- Docker готовность (при необходимости)

### Мониторинг

- Логирование через Prisma
- Error handling на всех уровнях
- Performance мониторинг через React Query DevTools

Этот технологический стек обеспечивает современную, масштабируемую и производительную архитектуру для приложения поиска попутчиков с полной типизацией, real-time функциональностью и отличным developer experience.