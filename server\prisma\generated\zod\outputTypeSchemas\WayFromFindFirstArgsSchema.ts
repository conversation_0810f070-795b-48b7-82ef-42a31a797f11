import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayFromIncludeSchema } from '../inputTypeSchemas/WayFromIncludeSchema'
import { WayFromWhereInputSchema } from '../inputTypeSchemas/WayFromWhereInputSchema'
import { WayFromOrderByWithRelationInputSchema } from '../inputTypeSchemas/WayFromOrderByWithRelationInputSchema'
import { WayFromWhereUniqueInputSchema } from '../inputTypeSchemas/WayFromWhereUniqueInputSchema'
import { WayFromScalarFieldEnumSchema } from '../inputTypeSchemas/WayFromScalarFieldEnumSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const WayFromSelectSchema: z.ZodType<Prisma.WayFromSelect> = z.object({
  id: z.boolean().optional(),
  geometa: z.boolean().optional(),
  date: z.boolean().optional(),
  lat: z.boolean().optional(),
  lon: z.boolean().optional(),
  caseId: z.boolean().optional(),
  comment: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
}).strict()

export const WayFromFindFirstArgsSchema: z.ZodType<Prisma.WayFromFindFirstArgs> = z.object({
  select: WayFromSelectSchema.optional(),
  include: z.lazy(() => WayFromIncludeSchema).optional(),
  where: WayFromWhereInputSchema.optional(),
  orderBy: z.union([ WayFromOrderByWithRelationInputSchema.array(),WayFromOrderByWithRelationInputSchema ]).optional(),
  cursor: WayFromWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ WayFromScalarFieldEnumSchema,WayFromScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export default WayFromFindFirstArgsSchema;
