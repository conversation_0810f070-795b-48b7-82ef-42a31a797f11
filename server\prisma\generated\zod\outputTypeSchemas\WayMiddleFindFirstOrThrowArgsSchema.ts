import { z } from 'zod';
import type { <PERSON>risma } from '@prisma/client';
import { WayMiddleIncludeSchema } from '../inputTypeSchemas/WayMiddleIncludeSchema'
import { WayMiddleWhereInputSchema } from '../inputTypeSchemas/WayMiddleWhereInputSchema'
import { WayMiddleOrderByWithRelationInputSchema } from '../inputTypeSchemas/WayMiddleOrderByWithRelationInputSchema'
import { WayMiddleWhereUniqueInputSchema } from '../inputTypeSchemas/WayMiddleWhereUniqueInputSchema'
import { WayMiddleScalarFieldEnumSchema } from '../inputTypeSchemas/WayMiddleScalarFieldEnumSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const WayMiddleSelectSchema: z.ZodType<Prisma.WayMiddleSelect> = z.object({
  id: z.boolean().optional(),
  geometa: z.boolean().optional(),
  date: z.boolean().optional(),
  lat: z.boolean().optional(),
  lon: z.boolean().optional(),
  caseId: z.boolean().optional(),
  comment: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
}).strict()

export const WayMiddleFindFirstOrThrowArgsSchema: z.ZodType<Prisma.WayMiddleFindFirstOrThrowArgs> = z.object({
  select: WayMiddleSelectSchema.optional(),
  include: z.lazy(() => WayMiddleIncludeSchema).optional(),
  where: WayMiddleWhereInputSchema.optional(),
  orderBy: z.union([ WayMiddleOrderByWithRelationInputSchema.array(),WayMiddleOrderByWithRelationInputSchema ]).optional(),
  cursor: WayMiddleWhereUniqueInputSchema.optional(),
  take: z.number().optional(),
  skip: z.number().optional(),
  distinct: z.union([ WayMiddleScalarFieldEnumSchema,WayMiddleScalarFieldEnumSchema.array() ]).optional(),
}).strict() ;

export default WayMiddleFindFirstOrThrowArgsSchema;
