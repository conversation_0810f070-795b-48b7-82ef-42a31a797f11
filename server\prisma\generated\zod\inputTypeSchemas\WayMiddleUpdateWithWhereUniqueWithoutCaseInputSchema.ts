import type { Prisma } from '@prisma/client';

import { z } from 'zod';
import { WayMiddleWhereUniqueInputSchema } from './WayMiddleWhereUniqueInputSchema';
import { WayMiddleUpdateWithoutCaseInputSchema } from './WayMiddleUpdateWithoutCaseInputSchema';
import { WayMiddleUncheckedUpdateWithoutCaseInputSchema } from './WayMiddleUncheckedUpdateWithoutCaseInputSchema';

export const WayMiddleUpdateWithWhereUniqueWithoutCaseInputSchema: z.ZodType<Prisma.WayMiddleUpdateWithWhereUniqueWithoutCaseInput> = z.object({
  where: z.lazy(() => WayMiddleWhereUniqueInputSchema),
  data: z.union([ z.lazy(() => WayMiddleUpdateWithoutCaseInputSchema),z.lazy(() => WayMiddleUncheckedUpdateWithoutCaseInputSchema) ]),
}).strict();

export default WayMiddleUpdateWithWhereUniqueWithoutCaseInputSchema;
