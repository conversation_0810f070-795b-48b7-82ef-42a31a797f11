import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayFromIncludeSchema } from '../inputTypeSchemas/WayFromIncludeSchema'
import { WayFromWhereUniqueInputSchema } from '../inputTypeSchemas/WayFromWhereUniqueInputSchema'
import { CaseArgsSchema } from "../outputTypeSchemas/CaseArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const WayFromSelectSchema: z.ZodType<Prisma.WayFromSelect> = z.object({
  id: z.boolean().optional(),
  geometa: z.boolean().optional(),
  date: z.boolean().optional(),
  lat: z.boolean().optional(),
  lon: z.boolean().optional(),
  caseId: z.boolean().optional(),
  comment: z.boolean().optional(),
  case: z.union([z.boolean(),z.lazy(() => CaseArgsSchema)]).optional(),
}).strict()

export const WayFromDeleteArgsSchema: z.ZodType<Prisma.WayFromDeleteArgs> = z.object({
  select: WayFromSelectSchema.optional(),
  include: z.lazy(() => WayFromIncludeSchema).optional(),
  where: WayFromWhereUniqueInputSchema,
}).strict() ;

export default WayFromDeleteArgsSchema;
