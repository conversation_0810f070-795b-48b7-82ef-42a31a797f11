import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { WayMiddleSelectSchema } from '../inputTypeSchemas/WayMiddleSelectSchema';
import { WayMiddleIncludeSchema } from '../inputTypeSchemas/WayMiddleIncludeSchema';

export const WayMiddleArgsSchema: z.ZodType<Prisma.WayMiddleDefaultArgs> = z.object({
  select: z.lazy(() => WayMiddleSelectSchema).optional(),
  include: z.lazy(() => WayMiddleIncludeSchema).optional(),
}).strict();

export default WayMiddleArgsSchema;
