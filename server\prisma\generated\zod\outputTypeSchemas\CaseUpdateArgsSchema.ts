import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { CaseIncludeSchema } from '../inputTypeSchemas/CaseIncludeSchema'
import { CaseUpdateInputSchema } from '../inputTypeSchemas/CaseUpdateInputSchema'
import { CaseUncheckedUpdateInputSchema } from '../inputTypeSchemas/CaseUncheckedUpdateInputSchema'
import { CaseWhereUniqueInputSchema } from '../inputTypeSchemas/CaseWhereUniqueInputSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
import { FavoriteFindManyArgsSchema } from "../outputTypeSchemas/FavoriteFindManyArgsSchema"
import { NotificationFindManyArgsSchema } from "../outputTypeSchemas/NotificationFindManyArgsSchema"
import { UserRatingFindManyArgsSchema } from "../outputTypeSchemas/UserRatingFindManyArgsSchema"
import { WayFromArgsSchema } from "../outputTypeSchemas/WayFromArgsSchema"
import { WayMiddleFindManyArgsSchema } from "../outputTypeSchemas/WayMiddleFindManyArgsSchema"
import { WayToArgsSchema } from "../outputTypeSchemas/WayToArgsSchema"
import { UserFindManyArgsSchema } from "../outputTypeSchemas/UserFindManyArgsSchema"
import { CaseCountOutputTypeArgsSchema } from "../outputTypeSchemas/CaseCountOutputTypeArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const CaseSelectSchema: z.ZodType<Prisma.CaseSelect> = z.object({
  id: z.boolean().optional(),
  createdAt: z.boolean().optional(),
  description: z.boolean().optional(),
  status: z.boolean().optional(),
  authorId: z.boolean().optional(),
  baggage: z.boolean().optional(),
  price: z.boolean().optional(),
  isRequest: z.boolean().optional(),
  expire_at: z.boolean().optional(),
  author: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
  favorites: z.union([z.boolean(),z.lazy(() => FavoriteFindManyArgsSchema)]).optional(),
  notifications: z.union([z.boolean(),z.lazy(() => NotificationFindManyArgsSchema)]).optional(),
  UserRating: z.union([z.boolean(),z.lazy(() => UserRatingFindManyArgsSchema)]).optional(),
  from: z.union([z.boolean(),z.lazy(() => WayFromArgsSchema)]).optional(),
  middlepoints: z.union([z.boolean(),z.lazy(() => WayMiddleFindManyArgsSchema)]).optional(),
  to: z.union([z.boolean(),z.lazy(() => WayToArgsSchema)]).optional(),
  clients: z.union([z.boolean(),z.lazy(() => UserFindManyArgsSchema)]).optional(),
  _count: z.union([z.boolean(),z.lazy(() => CaseCountOutputTypeArgsSchema)]).optional(),
}).strict()

export const CaseUpdateArgsSchema: z.ZodType<Prisma.CaseUpdateArgs> = z.object({
  select: CaseSelectSchema.optional(),
  include: z.lazy(() => CaseIncludeSchema).optional(),
  data: z.union([ CaseUpdateInputSchema,CaseUncheckedUpdateInputSchema ]),
  where: CaseWhereUniqueInputSchema,
}).strict() ;

export default CaseUpdateArgsSchema;
