import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsCreateManyInputSchema } from '../inputTypeSchemas/NotificationSettingsCreateManyInputSchema'

export const NotificationSettingsCreateManyArgsSchema: z.ZodType<Prisma.NotificationSettingsCreateManyArgs> = z.object({
  data: z.union([ NotificationSettingsCreateManyInputSchema,NotificationSettingsCreateManyInputSchema.array() ]),
  skipDuplicates: z.boolean().optional(),
}).strict() ;

export default NotificationSettingsCreateManyArgsSchema;
