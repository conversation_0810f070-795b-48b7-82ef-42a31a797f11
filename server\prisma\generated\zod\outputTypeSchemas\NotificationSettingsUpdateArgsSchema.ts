import { z } from 'zod';
import type { Prisma } from '@prisma/client';
import { NotificationSettingsIncludeSchema } from '../inputTypeSchemas/NotificationSettingsIncludeSchema'
import { NotificationSettingsUpdateInputSchema } from '../inputTypeSchemas/NotificationSettingsUpdateInputSchema'
import { NotificationSettingsUncheckedUpdateInputSchema } from '../inputTypeSchemas/NotificationSettingsUncheckedUpdateInputSchema'
import { NotificationSettingsWhereUniqueInputSchema } from '../inputTypeSchemas/NotificationSettingsWhereUniqueInputSchema'
import { UserArgsSchema } from "../outputTypeSchemas/UserArgsSchema"
// Select schema needs to be in file to prevent circular imports
//------------------------------------------------------

export const NotificationSettingsSelectSchema: z.ZodType<Prisma.NotificationSettingsSelect> = z.object({
  id: z.boolean().optional(),
  userId: z.boolean().optional(),
  method: z.boolean().optional(),
  active: z.boolean().optional(),
  user: z.union([z.boolean(),z.lazy(() => UserArgsSchema)]).optional(),
}).strict()

export const NotificationSettingsUpdateArgsSchema: z.ZodType<Prisma.NotificationSettingsUpdateArgs> = z.object({
  select: NotificationSettingsSelectSchema.optional(),
  include: z.lazy(() => NotificationSettingsIncludeSchema).optional(),
  data: z.union([ NotificationSettingsUpdateInputSchema,NotificationSettingsUncheckedUpdateInputSchema ]),
  where: NotificationSettingsWhereUniqueInputSchema,
}).strict() ;

export default NotificationSettingsUpdateArgsSchema;
